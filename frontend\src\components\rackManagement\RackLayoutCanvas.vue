<template>
  <div class="rack-layout-canvas">
    <div class="canvas-controls">
      <div class="left-controls">
      <el-button-group>
        <el-tooltip content="放大" placement="top" :show-after="200">
          <el-button size="small" @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="缩小" placement="top" :show-after="200">
          <el-button size="small" @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="重置视图" placement="top" :show-after="200">
          <el-button size="small" @click="resetZoom">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="拖拽模式" placement="top" :show-after="200">
          <el-button size="small" @click="toggleDragMode" :type="dragMode ? 'primary' : 'default'">
            <el-icon><Pointer /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'" placement="top" :show-after="200">
          <el-button size="small" @click="toggleFullscreen">
            <el-icon><FullScreen v-if="!isFullscreen" /><Aim v-else /></el-icon>
          </el-button>
        </el-tooltip>
      </el-button-group>
      
      <div class="zoom-slider-container">
        <el-icon><ZoomOut /></el-icon>
        <el-slider 
          v-model="zoomPercent" 
          :min="20" 
          :max="300" 
          :step="5" 
          :show-tooltip="false"
          @change="handleZoomSliderChange"
          class="zoom-slider"
        />
        <el-icon><ZoomIn /></el-icon>
        <span class="zoom-value">{{ zoomPercent }}%</span>
        </div>
      </div>
      
      <div class="right-controls">
        <div class="layout-controls">
          <span class="layout-label">每行机柜数:</span>
          <el-select 
            v-model="racksPerRow" 
            size="small" 
            style="width: 80px"
            @change="handleRacksPerRowChange"
          >
            <el-option 
              v-for="option in racksPerRowOptions" 
              :key="option" 
              :label="option" 
              :value="option" 
            />
          </el-select>
        </div>
        
        <el-button-group>
          <el-button size="small" :type="viewMode === 'front' ? 'primary' : ''" @click="viewMode = 'front'">
            前视图
          </el-button>
          <el-button size="small" :type="viewMode === 'back' ? 'primary' : ''" @click="viewMode = 'back'">
            后视图
          </el-button>
          <el-button size="small" :type="viewMode === '3d' ? 'primary' : ''" @click="viewMode = '3d'">
            3D视图
          </el-button>
        </el-button-group>
        
        <div class="operations-help">
          <el-popover
            placement="bottom-end"
            trigger="click"
            :width="320"
            title="操作指南"
          >
            <template #reference>
              <el-button size="small" circle>
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </template>
            <div class="operations-guide">
              <h4>鼠标操作说明：</h4>
              <ul class="operations-list">
                <li>
                  <el-icon><Mouse /></el-icon>
                  <span><b>滚轮滚动</b>：缩放视图</span>
                </li>
                <li>
                  <!-- <div class="op-icon"><b><span>Shift +</span></b><el-icon><Mouse /></el-icon></div> -->
                  <span><b>Shift + 滚轮</b>：左右水平滚动</span>
                </li>
                <li>
                  <el-icon><Pointer /></el-icon>
                  <span><b>按住滚轮</b>：拖动整个视图</span>
                </li>
                <li>
                  <el-icon><ZoomIn /></el-icon>
                  <span><b>点击设备</b>：查看设备详情</span>
                </li>
                <li>
                  <el-icon><Close /></el-icon>
                  <span><b>点击空白处或其他位置</b>：关闭弹窗</span>
                </li>
                <li>
                  <el-icon><FullScreen /></el-icon>
                  <span><b>全屏按钮</b>：切换全屏模式</span>
                </li>
              </ul>
            </div>
          </el-popover>
        </div>
      </div>
    </div>
    
        <div 
      class="rack-container" 
      ref="rackContainer"
      v-loading="loading"
      element-loading-text="布局刷新中..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
      @mousedown="handleMouseDown($event)"
      @mousemove="handleMouseMove($event)"
      @mouseup="stopDrag"
      @mouseleave="stopDrag"
      @wheel="handleWheel"
        >
      <div 
        class="rack-view" 
        v-if="racks && racks.length > 0"
        :style="{
          transform: `scale(${zoom}) translate(${panX}px, ${panY}px)`,
          transformOrigin: 'center top',
          cursor: dragMode ? (isDragging ? 'grabbing' : 'grab') : 'default'
        }"
      >
        <!-- 机柜分行显示 -->
        <template v-for="(rack, index) in racks" :key="rack.id">
          <!-- 每行开始添加行标记 -->
          <div v-if="index % racksPerRow === 0" class="row-indicator">
            第 {{ Math.floor(index / racksPerRow) + 1 }} 行
          </div>
          
          <!-- 机柜项 -->
          <div
            class="rack-item"
            :class="{ 'rack-selected': rack.id === selectedRackId }"
            :style="rackItemStyle"
            @click="handleRackClick(rack.id)"
          >
            <div class="rack-header">
              <div class="rack-name">{{ rack.name || `机柜 ${rack.id}` }}</div>
              <div class="rack-id">{{ rack.id }}</div>
            </div>
            <div class="rack-frame" :style="{ height: `${rack.total_u * 36}px` }">
              <div class="rack-numbers">
                <div v-for="u in rack.total_u" :key="`num-${u}`" class="u-number" :style="{ bottom: `${(u-1) * 36}px` }">
                  {{ u }}
                </div>
              </div>
              
              <div class="rack-devices">
                <el-popover
                  v-for="device in rack.devices" 
                  :key="device.id"
                  placement="right"
                  :width="300"
                  trigger="click"
                  popper-class="device-tooltip"
                  :hide-after="0"
                >
                  <template #default>
                    <div class="device-tooltip-content">
                      <h3>{{ device.name }}</h3>
                      <div class="tooltip-row">
                        <span class="tooltip-label">类型:</span>
                        <span>{{ getDeviceTypeLabel(device.type) }}</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">型号:</span>
                        <span>{{ device.model || '-' }}</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">序列号:</span>
                        <span class="tooltip-highlight">{{ device.serial_number || '-' }}</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">IP地址:</span>
                        <span class="tooltip-highlight">{{ device.ip_address || '-' }}</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">位置:</span>
                        <span>{{ device.position }}U</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">高度:</span>
                        <span>{{ device.u_size }}U</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">功率:</span>
                        <span>{{ device.power }}W</span>
                      </div>
                      <div class="tooltip-row">
                        <span class="tooltip-label">状态:</span>
                        <span :class="device.status === 'online' ? 'status-online' : 'status-offline'">
                          {{ device.status === 'online' ? '在线' : '离线' }}
                        </span>
                      </div>
                    </div>
                  </template>
                      
                  <template #reference>
                    <div 
                      class="device-item" 
                      :class="[`device-type-${device.type}`]"
                      :style="{ 
                        height: `${device.u_size * 36}px`, 
                        bottom: `${(device.position - 1) * 36}px` 
                      }"
                      @click="handleDeviceClick(device.id)"
                      @dblclick="handleDeviceDoubleClick(device.id)"
                    >
                      <div class="device-content">
                        <div class="device-name">{{ device.name }}</div>
                        <div class="device-actions">
                          <el-button 
                            v-if="showDeviceActions" 
                            type="primary" 
                            size="small" 
                            circle 
                            @click.stop="handleEditDevice(device.id)"
                          >
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button 
                            v-if="showDeviceActions" 
                            type="danger" 
                            size="small" 
                            circle 
                            @click.stop="handleRemoveDevice(device.id)"
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-popover>
                
                <!-- 可点击的空白U位 -->
                <div 
                  v-for="position in getEmptyPositions(rack)" 
                  :key="`empty-${position}`"
                  class="empty-slot"
                  :style="{ bottom: `${(position - 1) * 36}px` }"
                  @click="handleEmptySlotClick(position, rack.id)"
                >
                  <div class="empty-slot-content">
                    <el-icon><Plus /></el-icon>
                    <div class="empty-slot-text">{{ position }}U</div>
                  </div>
                </div>
              </div>
      
              <div class="rack-numbers right-numbers">
                <div v-for="u in rack.total_u" :key="`num-right-${u}`" class="u-number" :style="{ bottom: `${(u-1) * 36}px` }">
                  {{ u }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div v-else class="no-racks">
        <el-empty description="没有机柜数据">
          <el-button type="primary" @click="$emit('request-add-rack')">添加机柜</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  ZoomIn, ZoomOut, RefreshRight, Pointer, Delete, Edit, Plus,
  QuestionFilled, Mouse, Close, FullScreen, Aim
} from '@element-plus/icons-vue';

const props = defineProps({
  racks: {
    type: Array,
    required: true,
    default: () => []
  },
  selectedRackId: {
    type: String,
    default: null
  }
});

const emit = defineEmits([
  'select-device', 
  'remove-device', 
  'request-add-rack',
  'edit-device',
  'add-device-at-position',
  'select-rack'
]);

// 视图控制
const viewMode = ref('front');
const zoom = ref(1);
const zoomPercent = ref(100);
const dragMode = ref(false);
const isDragging = ref(false);
const panX = ref(0);
const panY = ref(0);
const startX = ref(0);
const startY = ref(0);
const rackContainer = ref(null);
const isFullscreen = ref(false);

// 每行显示的机柜数量
const racksPerRowOptions = [3, 4, 6, 8, 10, 12];
// 从localStorage读取用户首选的每行机柜数，如没有则默认为6
const savedRacksPerRow = localStorage.getItem('racksPerRow');
const racksPerRow = ref(savedRacksPerRow ? parseInt(savedRacksPerRow) : 6);

// 设备操作
const showDeviceActions = ref(false);
const loading = ref(false);

// 创建一个"刷新"效果
const refreshView = () => {
  // 添加一个短暂的加载状态
  loading.value = true;
  
  // 重置缩放到100%
  resetZoom();
  
  // 短暂延迟后完成布局计算
  setTimeout(() => {
    // 确保缩放比例为100%
    zoom.value = 1;
    zoomPercent.value = 100;
    
    // 居中显示，不自动适应
    if (rackContainer.value && props.racks.length > 0) {
      // 计算水平居中位置
      const containerWidth = rackContainer.value.clientWidth;
      const rackWidth = 250; // 单个机柜宽度
      const rackGap = 30;    // 机柜间隔
      const rowWidth = racksPerRow.value * rackWidth + (racksPerRow.value - 1) * rackGap;
      
      // 水平居中
      panX.value = (containerWidth/2 - rowWidth/2) / zoom.value;
      // 垂直从顶部开始
      panY.value = 0;

      // 应用边界约束
      constrainPan();
    }

    // 结束加载状态
    loading.value = false;
  }, 150);
};

// 处理改变每行机柜数
const handleRacksPerRowChange = (value) => {
  racksPerRow.value = value;
  // 保存用户首选项
  localStorage.setItem('racksPerRow', value.toString());
  
  // 添加加载状态
  loading.value = true;
  
  // 短暂延迟后模拟点击重置视图按钮
  setTimeout(() => {
    // 调用重置视图功能
    resetZoom();
    
    // 结束加载状态
    loading.value = false;
  }, 150);
};

// 不再需要动态计算机柜样式，使用固定宽度
const rackItemStyle = computed(() => {
  // 返回空对象，所有样式由CSS处理
  return {};
});

// 处理机柜点击
const handleRackClick = (rackId) => {
  emit('select-rack', rackId);
};

// 获取设备类型标签文本
const getDeviceTypeLabel = (type) => {
  switch (type) {
    case 'storage': return '存储';
    case 'server': return '服务器';
    case 'network': return '网络';
    case 'security': return '安全';
    default: return '其他';
  }
};

// 缩放控制
const zoomIn = () => {
  if (zoom.value < 3) {
      zoom.value += 0.1;
      zoomPercent.value = Math.round(zoom.value * 100);
    adjustViewAfterZoom();
    constrainPan();
  }
};

const zoomOut = () => {
  if (zoom.value > 0.2) {
      zoom.value -= 0.1;
      zoomPercent.value = Math.round(zoom.value * 100);
    adjustViewAfterZoom();
    constrainPan();
  }
};

const resetZoom = () => {
  zoom.value = 1;
  zoomPercent.value = 100;
  panX.value = 0;
  panY.value = 0;
  // 应用边界约束以确保内容在可见区域内
  setTimeout(() => {
    constrainPan();
  }, 10);
};

const handleZoomSliderChange = (value) => {
  zoom.value = value / 100;
  adjustViewAfterZoom();
  constrainPan();
};

// 调整视图以保持中心点
const adjustViewAfterZoom = () => {
  if (rackContainer.value) {
    const containerWidth = rackContainer.value.clientWidth;
    const containerHeight = rackContainer.value.clientHeight;
    
    // 确保放大后视图仍然居中
    if (panX.value !== 0 || panY.value !== 0) {
      // 调整平移量，保持视图中心
      const adjustFactor = 0.05; // 微调因子
      panX.value = panX.value * (1 + adjustFactor);
      panY.value = panY.value * (1 + adjustFactor);
    }
  }
};

// 拖拽控制
const toggleDragMode = () => {
  dragMode.value = !dragMode.value;
};

// 全屏控制
const toggleFullscreen = async () => {
  try {
    if (isFullscreen.value) {
      await document.exitFullscreen();
    } else {
      if (rackContainer.value && rackContainer.value.requestFullscreen) {
        await rackContainer.value.requestFullscreen();
      } else {
        console.warn('Fullscreen API not supported');
      }
    }
  } catch (error) {
    console.error('Fullscreen operation failed:', error);
  }
};

// 处理全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
  // 全屏状态变化后重新计算边界并应用约束
  setTimeout(() => {
    calculateAndApplyBoundaries();
  }, 100);
};

// 计算平移边界
const calculatePanBoundaries = () => {
  if (!rackContainer.value || !props.racks.length) return null;

  const containerWidth = rackContainer.value.clientWidth;
  const containerHeight = rackContainer.value.clientHeight;

  // 计算内容尺寸
  const rackWidth = 250;
  const rackGap = 30;
  const rowWidth = racksPerRow.value * rackWidth + (racksPerRow.value - 1) * rackGap;
  const rowCount = Math.ceil(props.racks.length / racksPerRow.value);
  const maxU = Math.max(...props.racks.map(rack => rack.total_u || 42));
  const rackHeight = maxU * 36;
  const totalHeight = rowCount * (rackHeight + 80) + 40; // 额外的边距

  // 考虑缩放后的内容尺寸
  const scaledWidth = rowWidth * zoom.value;
  const scaledHeight = totalHeight * zoom.value;

  // 最小可见区域（确保内容至少部分可见）
  const minVisibleArea = 100;

  // 计算边界
  const maxPanX = (containerWidth - minVisibleArea) / zoom.value;
  const minPanX = (minVisibleArea - scaledWidth) / zoom.value;
  const maxPanY = (containerHeight - minVisibleArea) / zoom.value;
  const minPanY = (minVisibleArea - scaledHeight) / zoom.value;

  return { minPanX, maxPanX, minPanY, maxPanY };
};

// 应用平移约束
const constrainPan = () => {
  const boundaries = calculatePanBoundaries();
  if (!boundaries) return;

  // 确保边界值是有效的数字
  if (isFinite(boundaries.minPanX) && isFinite(boundaries.maxPanX)) {
    panX.value = Math.max(boundaries.minPanX, Math.min(boundaries.maxPanX, panX.value));
  }
  if (isFinite(boundaries.minPanY) && isFinite(boundaries.maxPanY)) {
    panY.value = Math.max(boundaries.minPanY, Math.min(boundaries.maxPanY, panY.value));
  }
};

// 计算并应用边界约束
const calculateAndApplyBoundaries = () => {
  constrainPan();
};

// 处理鼠标按下事件
const handleMouseDown = (event) => {
  // 检查是否是中键(滑轮)点击 (button === 1) 或已启用拖拽模式
  const isMiddleButton = event.button === 1;
  
  if (isMiddleButton || dragMode.value) {
    // 如果是滑轮点击，启用临时拖拽模式
    isDragging.value = true;
    startX.value = event.clientX;
    startY.value = event.clientY;
    
    // 更改鼠标样式
    if (rackContainer.value) {
      rackContainer.value.style.cursor = 'grabbing';
    }
    
    // 阻止默认滑轮点击行为(通常是自动滚动)
    if (isMiddleButton) {
      event.preventDefault();
    }
  }
};

// 处理鼠标移动事件
const handleMouseMove = (event) => {
  if (!isDragging.value) return;

  const dx = (event.clientX - startX.value) / zoom.value;
  const dy = (event.clientY - startY.value) / zoom.value;

  panX.value += dx;
  panY.value += dy;

  // 应用边界约束
  constrainPan();

  startX.value = event.clientX;
  startY.value = event.clientY;
};

const stopDrag = () => {
  isDragging.value = false;
  
  // 恢复鼠标样式
  if (rackContainer.value) {
    rackContainer.value.style.cursor = dragMode.value ? 'grab' : 'default';
  }
};

// 鼠标滚轮控制
const handleWheel = (event) => {
  event.preventDefault();
  
  // 按住Shift键时水平滚动，否则缩放
  if (event.shiftKey) {
    const scrollAmount = event.deltaY > 0 ? -30 : 30;
    panX.value += scrollAmount / zoom.value;
    // 应用边界约束
    constrainPan();
  } else {
    // 缩放，以鼠标位置为中心
    const delta = event.deltaY > 0 ? -0.1 : 0.1;
    const newZoom = Math.max(0.2, Math.min(3, zoom.value + delta));
    
    if (newZoom !== zoom.value) {
      // 获取鼠标在容器中的相对位置
      const rect = event.currentTarget.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;
      
      // 计算鼠标位置相对于内容的坐标
      const contentX = mouseX / zoom.value - panX.value;
      const contentY = mouseY / zoom.value - panY.value;
      
      // 设置新缩放值
      zoom.value = newZoom;
      zoomPercent.value = Math.round(newZoom * 100);
      
      // 调整平移量，使鼠标位置不变
      panX.value = mouseX / newZoom - contentX;
      panY.value = mouseY / newZoom - contentY;

      // 应用边界约束
      constrainPan();
    }
  }
};

// 适应所有机柜
const fitAllRacks = () => {
  if (!props.racks || props.racks.length === 0 || !rackContainer.value) {
    resetZoom();
    return;
  }
  
  // 容器尺寸
  const containerWidth = rackContainer.value.clientWidth;
  const containerHeight = rackContainer.value.clientHeight;
  
  // 计算行数
  const rowCount = Math.ceil(props.racks.length / racksPerRow.value);
  
  // 使用固定的机柜宽度
  const rackWidth = 250; // 与CSS中的宽度保持一致
  const rackGap = 30;
  const rowWidth = racksPerRow.value * rackWidth + (racksPerRow.value - 1) * rackGap;
  
  // 计算机柜高度
  const maxU = Math.max(...props.racks.map(rack => rack.total_u || 42));
  const rackHeight = maxU * 36;
  const rowHeight = rackHeight + 80; // 考虑行标记的高度和边距
  const totalHeight = rowCount * rowHeight;
  
  // 计算缩放比例
  const scaleX = containerWidth / (rowWidth + 60); // 左右两侧各留出30px
  const scaleY = containerHeight / (totalHeight + 60);
  const newZoom = Math.min(1, Math.max(0.2, Math.min(scaleX, scaleY)));
  
  zoom.value = newZoom;
  zoomPercent.value = Math.round(newZoom * 100);
  
  // 居中显示
  panX.value = (containerWidth / newZoom - rowWidth) / 2;
  panY.value = 0;

  // 应用边界约束
  constrainPan();
};

// 设备操作
const handleDeviceClick = (deviceId) => {
  // 找到设备对象
  const device = props.racks
    .flatMap(rack => rack.devices)
    .find(d => d.id === deviceId);
    
  if (device) {
    emit('select-device', device);
  } else {
    console.error(`设备ID ${deviceId} 不存在`);
  }
};
  
const handleDeviceDoubleClick = (deviceId) => {
  showDeviceActions.value = !showDeviceActions.value;
};

const handleRemoveDevice = (deviceId) => {
  if (!deviceId) {
    console.error('无效的设备ID');
    return;
  }
  
  console.log(`触发从机柜视图移除设备事件，设备ID: ${deviceId}`);
  emit('remove-device', deviceId);
};

const handleEditDevice = (deviceId) => {
  if (!deviceId) {
    console.error('无效的设备ID');
    return;
  }
  
  // 找到设备对象
  const device = props.racks
    .flatMap(rack => rack.devices)
    .find(d => d.id === deviceId);
    
  if (device) {
    console.log(`触发编辑设备事件，设备ID: ${deviceId}`);
    emit('edit-device', device);
  } else {
    console.error(`设备ID ${deviceId} 不存在`);
  }
};

const getEmptyPositions = (rack) => {
  // 创建一个表示所有U位状态的数组
  const occupiedPositions = new Array(rack.total_u).fill(false);
  
  // 打印设备信息以便调试
  console.log('机柜设备:', rack.devices);
  
  // 标记已被占用的U位
  if (rack.devices && Array.isArray(rack.devices)) {
    rack.devices.forEach(device => {
      console.log(`处理设备 ${device.name}, 位置: ${device.position}, 高度: ${device.u_size}U`);
      
      // 设备占用的范围是从position到position+u_size-1
      const bottomPosition = device.position; // 底部位置
      const topPosition = device.position + device.u_size - 1; // 顶部位置
      
      console.log(`设备占用从 ${bottomPosition}U 到 ${topPosition}U`);
      
      // 将这个范围内的所有位置都标记为已占用
      for (let pos = bottomPosition; pos <= topPosition; pos++) {
        if (pos > 0 && pos <= rack.total_u) {
          console.log(`标记位置 ${pos}U 为已占用`);
          occupiedPositions[pos - 1] = true;
        }
      }
    });
  }
  
  // 获取未被占用的U位
  const emptyPositions = [];
  occupiedPositions.forEach((isOccupied, index) => {
    if (!isOccupied) {
      emptyPositions.push(index + 1); // 转换回1-based索引
    }
  });
  
  console.log('空位列表:', emptyPositions);
  return emptyPositions;
};

const handleEmptySlotClick = (position, rackId) => {
  console.log(`添加设备到位置: ${position}U, 机柜ID: ${rackId}`);
  emit('add-device-at-position', { position, rackId });
};

onMounted(() => {
  console.log("RackLayoutCanvas mounted, racks:", props.racks);
  fitAllRacks();

  // 监听窗口大小变化，重新计算布局
  window.addEventListener('resize', fitAllRacks);

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);

  // 组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', fitAllRacks);
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
  });
});
</script>

<style scoped>
.rack-layout-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.canvas-controls {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  margin-bottom: 10px;
  position: relative;
}

.operations-help {
  display: flex;
  align-items: center;
  z-index: 10;
}

.operations-guide {
  padding: 8px 4px;
}

.operations-guide h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  color: #303133;
}

.operations-list {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.operations-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 13px;
  color: #606266;
}

.operations-list .el-icon {
  font-size: 16px;
  color: #409EFF;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.op-icon {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 20px;
  font-size: 12px;
}

.left-controls, .right-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.layout-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 5px;
}

.layout-label {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
}

.zoom-slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 200px;
}

.zoom-slider {
  flex: 1;
}

.zoom-value {
  font-size: 12px;
  color: #606266;
  width: 40px;
  text-align: right;
}

.rack-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  user-select: none; /* 防止拖动时选择文本 */
}

/* 全屏模式样式 */
.rack-container:fullscreen {
  background-color: #f5f7fa;
  border: none;
  border-radius: 0;
}

.rack-container:fullscreen .canvas-controls {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  border-bottom: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.rack-view {
  min-height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  padding: 20px;
  transition: transform 0.1s ease-out;
  will-change: transform;
  justify-content: flex-start;
  align-content: flex-start;
  width: max-content; /* 使用max-content确保足够宽度 */
  margin: 0 auto; /* 在容器中居中 */
}

.row-indicator {
  width: 100%;
  padding: 10px;
  background-color: #f0f6ff;
  color: #1e88e5;
  font-weight: bold;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: left;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #1e88e5;
}

.rack-item {
  background-color: #e0e0e0;
  border: 2px solid #bdbdbd;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  flex-shrink: 0;
  flex-grow: 0; /* 不自动增长 */
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  width: 250px; /* 固定宽度，所有机柜保持一致 */
}

.rack-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.rack-item.rack-selected {
  border: 2px solid #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.rack-header {
  background: linear-gradient(to right, #1e88e5, #42a5f5);
  color: white;
  padding: 8px 10px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%; /* 使用百分比宽度，适应不同屏幕大小 */
}

.rack-name {
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rack-id {
  font-size: 11px;
  opacity: 0.8;
}

.rack-frame {
  width: 250px; /* 固定宽度，与rack-item保持一致 */
  position: relative;
  display: flex;
}

.rack-numbers {
  width: 30px;
  background-color: #d0d0d0;
  position: relative;
}

.right-numbers {
  border-left: 1px solid #bdbdbd;
}

.u-number {
  position: absolute;
  height: 36px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #606266;
  border-bottom: 1px dashed #bdbdbd;
}

.rack-devices {
  flex: 1;
  background-color: #f5f5f5;
  position: relative;
}

.device-item {
  position: absolute;
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  transition: all 0.3s;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-item:hover {
  transform: translateZ(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.device-content {
  padding: 5px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.device-name {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
}

.device-type-server {
  background-color: #e3f2fd;
  border-color: #bbdefb;
}

.device-type-network {
  background-color: #e8f5e9;
  border-color: #c8e6c9;
}

.device-type-storage {
  background-color: #fff3e0;
  border-color: #ffe0b2;
}

.device-type-security {
  background-color: #ffebee;
  border-color: #ffcdd2;
}

.device-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 5px;
}

.no-racks {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 设备提示框样式 */
.device-tooltip-content {
  padding: 5px;
}

.device-tooltip-content h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.tooltip-row {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
}

.tooltip-label {
  color: #909399;
  width: 60px;
  text-align: right;
  padding-right: 10px;
}

.tooltip-highlight {
  color: #409eff;
  font-weight: bold;
}

.status-online {
  color: #67c23a;
  font-weight: bold;
}

.status-offline {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-popover.device-tooltip) {
  padding: 10px;
  max-width: 300px;
}

.empty-slot {
  position: absolute;
  left: 0;
  right: 0;
  height: 36px;
  background-color: rgba(236, 245, 255, 0.6);
  border: 1px dashed #a0cfff;
  transition: all 0.3s;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-slot:hover {
  background-color: #ecf5ff;
  border: 1px dashed #409eff;
  z-index: 2;
}

.empty-slot-content {
  padding: 5px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.empty-slot-text {
  font-size: 12px;
  color: #409eff;
}
</style> 