2025-08-03 12:13:36,320 [INFO] 使用Nornir进行巡检，无需初始化工作线程池，并发数: 20
2025-08-03 12:13:36,352 [WARNING] PDF生成库未安装: No module named 'reportlab'
2025-08-03 12:13:36,894 [INFO] Python路径: ['G:\\webguanli_sc7-21', 'G:\\webguanli_sc7-21', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312', 'G:\\webguanli_sc7-21\\myvenv', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\win32', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\win32\\lib', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\Pythonwin']
2025-08-03 12:13:36,895 [INFO] Python版本: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-08-03 12:13:36,895 [INFO] 当前工作目录: G:\webguanli_sc7-21
2025-08-03 12:13:36,895 [INFO] 尝试从 netmiko.ssh_exception 导入异常类
2025-08-03 12:13:36,896 [WARNING] 从 netmiko.ssh_exception 导入异常类失败
2025-08-03 12:13:36,896 [INFO] 尝试从 netmiko.exceptions 导入异常类
2025-08-03 12:13:36,896 [INFO] 从 netmiko.exceptions 成功导入异常类
2025-08-03 12:13:37,004 [INFO] WebSSH module initialized
2025-08-03 12:13:37,056 [INFO] === 应用启动 ===
2025-08-03 12:13:37,056 [INFO] Python版本: 3.12.4
2025-08-03 12:13:37,056 [INFO] 操作系统: Windows 11
2025-08-03 12:13:37,056 [INFO] 检查导入的模块...
2025-08-03 12:13:37,056 [INFO] FastAPI版本: 0.115.13
2025-08-03 12:13:37,057 [INFO] SQLAlchemy引擎: Engine(mysql+pymysql://root:***@localhost/netconfig)
2025-08-03 12:13:37,057 [INFO] 模块导入检查完成
2025-08-03 12:13:37,057 [INFO] 正在创建不存在的数据库表...
2025-08-03 12:13:52,115 [INFO] 使用Nornir进行巡检，无需初始化工作线程池，并发数: 20
2025-08-03 12:13:52,144 [WARNING] PDF生成库未安装: No module named 'reportlab'
2025-08-03 12:13:52,638 [INFO] Python路径: ['G:\\webguanli_sc7-21', 'G:\\webguanli_sc7-21', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312', 'G:\\webguanli_sc7-21\\myvenv', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\win32', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\win32\\lib', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\Pythonwin']
2025-08-03 12:13:52,640 [INFO] Python版本: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-08-03 12:13:52,640 [INFO] 当前工作目录: G:\webguanli_sc7-21
2025-08-03 12:13:52,640 [INFO] 尝试从 netmiko.ssh_exception 导入异常类
2025-08-03 12:13:52,641 [WARNING] 从 netmiko.ssh_exception 导入异常类失败
2025-08-03 12:13:52,641 [INFO] 尝试从 netmiko.exceptions 导入异常类
2025-08-03 12:13:52,641 [INFO] 从 netmiko.exceptions 成功导入异常类
2025-08-03 12:13:52,762 [INFO] WebSSH module initialized
2025-08-03 12:13:52,830 [INFO] === 应用启动 ===
2025-08-03 12:13:52,830 [INFO] Python版本: 3.12.4
2025-08-03 12:13:52,830 [INFO] 操作系统: Windows 11
2025-08-03 12:13:52,831 [INFO] 检查导入的模块...
2025-08-03 12:13:52,832 [INFO] FastAPI版本: 0.115.13
2025-08-03 12:13:52,832 [INFO] SQLAlchemy引擎: Engine(mysql+pymysql://root:***@localhost/netconfig)
2025-08-03 12:13:52,832 [INFO] 模块导入检查完成
2025-08-03 12:13:52,832 [INFO] 正在创建不存在的数据库表...
2025-08-03 12:13:52,839 [INFO] SELECT DATABASE()
2025-08-03 12:13:52,839 [INFO] [raw sql] {}
2025-08-03 12:13:52,840 [INFO] SELECT @@sql_mode
2025-08-03 12:13:52,840 [INFO] [raw sql] {}
2025-08-03 12:13:52,840 [INFO] SELECT @@lower_case_table_names
2025-08-03 12:13:52,841 [INFO] [raw sql] {}
2025-08-03 12:13:52,841 [INFO] BEGIN (implicit)
2025-08-03 12:13:52,843 [INFO] DESCRIBE `netconfig`.`devices`
2025-08-03 12:13:52,843 [INFO] [raw sql] {}
2025-08-03 12:13:52,849 [INFO] DESCRIBE `netconfig`.`configs`
2025-08-03 12:13:52,849 [INFO] [raw sql] {}
2025-08-03 12:13:52,852 [INFO] DESCRIBE `netconfig`.`template_configs`
2025-08-03 12:13:52,852 [INFO] [raw sql] {}
2025-08-03 12:13:52,856 [INFO] DESCRIBE `netconfig`.`config_history`
2025-08-03 12:13:52,856 [INFO] [raw sql] {}
2025-08-03 12:13:52,859 [INFO] DESCRIBE `netconfig`.`command_templates`
2025-08-03 12:13:52,859 [INFO] [raw sql] {}
2025-08-03 12:13:52,863 [INFO] DESCRIBE `netconfig`.`batch_config_history`
2025-08-03 12:13:52,864 [INFO] [raw sql] {}
2025-08-03 12:13:52,866 [INFO] DESCRIBE `netconfig`.`configurations`
2025-08-03 12:13:52,867 [INFO] [raw sql] {}
2025-08-03 12:13:52,870 [INFO] DESCRIBE `netconfig`.`subnets`
2025-08-03 12:13:52,870 [INFO] [raw sql] {}
2025-08-03 12:13:52,872 [INFO] DESCRIBE `netconfig`.`ip_addresses`
2025-08-03 12:13:52,872 [INFO] [raw sql] {}
2025-08-03 12:13:52,876 [INFO] DESCRIBE `netconfig`.`racks`
2025-08-03 12:13:52,876 [INFO] [raw sql] {}
2025-08-03 12:13:52,878 [INFO] DESCRIBE `netconfig`.`rack_devices`
2025-08-03 12:13:52,879 [INFO] [raw sql] {}
2025-08-03 12:13:52,882 [INFO] DESCRIBE `netconfig`.`device_port_configs`
2025-08-03 12:13:52,882 [INFO] [raw sql] {}
2025-08-03 12:13:52,886 [INFO] DESCRIBE `netconfig`.`inspection_results`
2025-08-03 12:13:52,886 [INFO] [raw sql] {}
2025-08-03 12:13:52,889 [INFO] DESCRIBE `netconfig`.`datacenters`
2025-08-03 12:13:52,889 [INFO] [raw sql] {}
2025-08-03 12:13:52,891 [INFO] DESCRIBE `netconfig`.`ai_settings`
2025-08-03 12:13:52,891 [INFO] [raw sql] {}
2025-08-03 12:13:52,894 [INFO] DESCRIBE `netconfig`.`ai_conversations`
2025-08-03 12:13:52,894 [INFO] [raw sql] {}
2025-08-03 12:13:52,896 [INFO] DESCRIBE `netconfig`.`ai_messages`
2025-08-03 12:13:52,896 [INFO] [raw sql] {}
2025-08-03 12:13:52,898 [INFO] DESCRIBE `netconfig`.`ip_conflict_detection_sessions`
2025-08-03 12:13:52,900 [INFO] [raw sql] {}
2025-08-03 12:13:52,902 [INFO] DESCRIBE `netconfig`.`ip_conflict_records`
2025-08-03 12:13:52,902 [INFO] [raw sql] {}
2025-08-03 12:13:52,905 [INFO] DESCRIBE `netconfig`.`ip_conflict_device_details`
2025-08-03 12:13:52,905 [INFO] [raw sql] {}
2025-08-03 12:13:52,908 [INFO] DESCRIBE `netconfig`.`ip_conflict_resolution_logs`
2025-08-03 12:13:52,908 [INFO] [raw sql] {}
2025-08-03 12:13:52,911 [INFO] COMMIT
2025-08-03 12:13:52,912 [INFO] BEGIN (implicit)
2025-08-03 12:13:52,912 [INFO] SHOW FULL TABLES FROM `netconfig`
2025-08-03 12:13:52,913 [INFO] [raw sql] {}
2025-08-03 12:13:52,916 [INFO] ROLLBACK
2025-08-03 12:13:52,917 [INFO] 检查configurations表结构...
2025-08-03 12:13:52,925 [INFO] BEGIN (implicit)
2025-08-03 12:13:52,926 [INFO] SHOW CREATE TABLE `configurations`
2025-08-03 12:13:52,926 [INFO] [raw sql] {}
2025-08-03 12:13:52,932 [INFO] ROLLBACK
2025-08-03 12:13:52,934 [INFO] 完成数据库表创建检查
2025-08-03 12:13:53,085 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,086 [INFO] SELECT 1
2025-08-03 12:13:53,086 [INFO] [generated in 0.00115s] {}
2025-08-03 12:13:53,087 [INFO] ROLLBACK
2025-08-03 12:13:53,087 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,087 [INFO] DESCRIBE `netconfig`.`devices`
2025-08-03 12:13:53,088 [INFO] [raw sql] {}
2025-08-03 12:13:53,089 [INFO] DESCRIBE `netconfig`.`configs`
2025-08-03 12:13:53,089 [INFO] [raw sql] {}
2025-08-03 12:13:53,091 [INFO] DESCRIBE `netconfig`.`template_configs`
2025-08-03 12:13:53,091 [INFO] [raw sql] {}
2025-08-03 12:13:53,092 [INFO] DESCRIBE `netconfig`.`config_history`
2025-08-03 12:13:53,092 [INFO] [raw sql] {}
2025-08-03 12:13:53,094 [INFO] DESCRIBE `netconfig`.`command_templates`
2025-08-03 12:13:53,094 [INFO] [raw sql] {}
2025-08-03 12:13:53,095 [INFO] DESCRIBE `netconfig`.`batch_config_history`
2025-08-03 12:13:53,095 [INFO] [raw sql] {}
2025-08-03 12:13:53,096 [INFO] DESCRIBE `netconfig`.`configurations`
2025-08-03 12:13:53,097 [INFO] [raw sql] {}
2025-08-03 12:13:53,098 [INFO] DESCRIBE `netconfig`.`subnets`
2025-08-03 12:13:53,098 [INFO] [raw sql] {}
2025-08-03 12:13:53,100 [INFO] DESCRIBE `netconfig`.`ip_addresses`
2025-08-03 12:13:53,100 [INFO] [raw sql] {}
2025-08-03 12:13:53,101 [INFO] DESCRIBE `netconfig`.`racks`
2025-08-03 12:13:53,101 [INFO] [raw sql] {}
2025-08-03 12:13:53,103 [INFO] DESCRIBE `netconfig`.`rack_devices`
2025-08-03 12:13:53,103 [INFO] [raw sql] {}
2025-08-03 12:13:53,104 [INFO] DESCRIBE `netconfig`.`device_port_configs`
2025-08-03 12:13:53,105 [INFO] [raw sql] {}
2025-08-03 12:13:53,106 [INFO] DESCRIBE `netconfig`.`inspection_results`
2025-08-03 12:13:53,106 [INFO] [raw sql] {}
2025-08-03 12:13:53,107 [INFO] DESCRIBE `netconfig`.`datacenters`
2025-08-03 12:13:53,108 [INFO] [raw sql] {}
2025-08-03 12:13:53,109 [INFO] DESCRIBE `netconfig`.`ai_settings`
2025-08-03 12:13:53,109 [INFO] [raw sql] {}
2025-08-03 12:13:53,110 [INFO] DESCRIBE `netconfig`.`ai_conversations`
2025-08-03 12:13:53,110 [INFO] [raw sql] {}
2025-08-03 12:13:53,112 [INFO] DESCRIBE `netconfig`.`ai_messages`
2025-08-03 12:13:53,112 [INFO] [raw sql] {}
2025-08-03 12:13:53,113 [INFO] DESCRIBE `netconfig`.`ip_conflict_detection_sessions`
2025-08-03 12:13:53,113 [INFO] [raw sql] {}
2025-08-03 12:13:53,114 [INFO] DESCRIBE `netconfig`.`ip_conflict_records`
2025-08-03 12:13:53,114 [INFO] [raw sql] {}
2025-08-03 12:13:53,115 [INFO] DESCRIBE `netconfig`.`ip_conflict_device_details`
2025-08-03 12:13:53,115 [INFO] [raw sql] {}
2025-08-03 12:13:53,117 [INFO] DESCRIBE `netconfig`.`ip_conflict_resolution_logs`
2025-08-03 12:13:53,117 [INFO] [raw sql] {}
2025-08-03 12:13:53,118 [INFO] COMMIT
2025-08-03 12:13:53,118 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,119 [INFO] 
                CREATE TABLE IF NOT EXISTS inspection_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    device_id INT NOT NULL,
                    inspection_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    status VARCHAR(50) NOT NULL COMMENT '巡检状态: success, failure, timeout',
                    file_path VARCHAR(500) NULL COMMENT '结果文件路径',
                    file_exists BOOLEAN NOT NULL DEFAULT 1 COMMENT '文件是否存在',
                    cpu_usage FLOAT NULL COMMENT 'CPU使用率',
                    memory_usage FLOAT NULL COMMENT '内存使用率',
                    interface_status TEXT NULL COMMENT '接口状态JSON',
                    summary TEXT NULL COMMENT '巡检摘要',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_device_id (device_id),
                    INDEX idx_inspection_time (inspection_time),
                    INDEX idx_status (status),
                    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡检历史记录表';
            
2025-08-03 12:13:53,119 [INFO] [generated in 0.00096s] {}
2025-08-03 12:13:53,148 [INFO] ROLLBACK
2025-08-03 12:13:53,168 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,171 [INFO] SELECT count(*) AS count_1 
FROM (SELECT configs.id AS configs_id, configs.config_name AS configs_config_name, configs.config_content AS configs_config_content, configs.config_type AS configs_config_type, configs.vendor AS configs_vendor, configs.is_deployed AS configs_is_deployed, configs.created_at AS configs_created_at, configs.updated_at AS configs_updated_at 
FROM configs) AS anon_1
2025-08-03 12:13:53,172 [INFO] [generated in 0.00050s] {}
2025-08-03 12:13:53,177 [INFO] ROLLBACK
2025-08-03 12:13:53,178 [INFO] 正在初始化机柜管理模块...
2025-08-03 12:13:53,178 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,178 [INFO] DESCRIBE `netconfig`.`racks`
2025-08-03 12:13:53,178 [INFO] [raw sql] {}
2025-08-03 12:13:53,180 [INFO] DESCRIBE `netconfig`.`rack_devices`
2025-08-03 12:13:53,180 [INFO] [raw sql] {}
2025-08-03 12:13:53,182 [INFO] COMMIT
2025-08-03 12:13:53,182 [INFO] 机柜管理表创建成功
2025-08-03 12:13:53,183 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,188 [INFO] SELECT count(*) AS count_1 
FROM (SELECT racks.id AS racks_id, racks.name AS racks_name, racks.location AS racks_location, racks.datacenter_id AS racks_datacenter_id, racks.total_u AS racks_total_u, racks.used_u AS racks_used_u, racks.width AS racks_width, racks.depth AS racks_depth, racks.temperature AS racks_temperature, racks.humidity AS racks_humidity, racks.power AS racks_power, racks.max_power AS racks_max_power, racks.status AS racks_status, racks.description AS racks_description, racks.created_at AS racks_created_at, racks.updated_at AS racks_updated_at 
FROM racks) AS anon_1
2025-08-03 12:13:53,189 [INFO] [generated in 0.00121s] {}
2025-08-03 12:13:53,199 [INFO] 已存在 3 个机柜，跳过示例数据创建
2025-08-03 12:13:53,200 [INFO] ROLLBACK
2025-08-03 12:13:53,201 [INFO] 机柜管理模块初始化完成
2025-08-03 12:13:53,203 [INFO] 路由: /, 方法: {'GET'}, 名称: root
2025-08-03 12:13:53,203 [INFO] 路由: /api, 方法: {'GET'}, 名称: read_api_root
2025-08-03 12:13:53,203 [INFO] 路由: /api/, 方法: {'GET'}, 名称: api_root
2025-08-03 12:13:53,203 [INFO] 路由: /api/, 方法: {'GET'}, 名称: get_topology_data
2025-08-03 12:13:53,203 [INFO] 路由: /api/, 方法: {'GET'}, 名称: get_users
2025-08-03 12:13:53,203 [INFO] 路由: /api/add-test-devices, 方法: {'POST'}, 名称: add_test_devices
2025-08-03 12:13:53,204 [INFO] 路由: /api/ai-operations/ai-service/status, 方法: {'GET'}, 名称: check_ai_service_status
2025-08-03 12:13:53,205 [INFO] 路由: /api/ai-operations/alerts, 方法: {'GET'}, 名称: get_alerts
2025-08-03 12:13:53,205 [INFO] 路由: /api/ai-operations/alerts/analyze, 方法: {'POST'}, 名称: analyze_alert
2025-08-03 12:13:53,205 [INFO] 路由: /api/ai-operations/analyze-file, 方法: {'POST'}, 名称: analyze_file
2025-08-03 12:13:53,206 [INFO] 路由: /api/ai-operations/anomalies, 方法: {'GET'}, 名称: get_anomalies
2025-08-03 12:13:53,206 [INFO] 路由: /api/ai-operations/anomalies/analyze, 方法: {'POST'}, 名称: analyze_anomaly
2025-08-03 12:13:53,206 [INFO] 路由: /api/ai-operations/automation-tasks, 方法: {'GET'}, 名称: get_automation_tasks
2025-08-03 12:13:53,206 [INFO] 路由: /api/ai-operations/device-predictions, 方法: {'GET'}, 名称: get_device_predictions
2025-08-03 12:13:53,206 [INFO] 路由: /api/ai-operations/device-predictions/enhanced, 方法: {'GET'}, 名称: get_enhanced_device_predictions
2025-08-03 12:13:53,207 [INFO] 路由: /api/ai-operations/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:13:53,207 [INFO] 路由: /api/ai-operations/health-metrics, 方法: {'GET'}, 名称: get_health_metrics
2025-08-03 12:13:53,207 [INFO] 路由: /api/ai-operations/knowledge-base/query, 方法: {'POST'}, 名称: knowledge_base_query
2025-08-03 12:13:53,207 [INFO] 路由: /api/ai-operations/logs/analyze, 方法: {'POST'}, 名称: analyze_log_file
2025-08-03 12:13:53,207 [INFO] 路由: /api/ai-webssh-test, 方法: {'GET'}, 名称: ai_webssh_test
2025-08-03 12:13:53,207 [INFO] 路由: /api/ai-webssh/analyze-output, 方法: {'POST'}, 名称: webssh_ai_analyze_output
2025-08-03 12:13:53,208 [INFO] 路由: /api/ai-webssh/chat, 方法: {'POST'}, 名称: webssh_ai_chat
2025-08-03 12:13:53,208 [INFO] 路由: /api/ai-webssh/test, 方法: {'GET'}, 名称: webssh_ai_test
2025-08-03 12:13:53,208 [INFO] 路由: /api/ai/chat, 方法: {'POST'}, 名称: ai_chat_proxy
2025-08-03 12:13:53,208 [INFO] 路由: /api/ai/chat, 方法: {'POST'}, 名称: chat
2025-08-03 12:13:53,208 [INFO] 路由: /api/ai/conversations, 方法: {'GET'}, 名称: get_conversations
2025-08-03 12:13:53,208 [INFO] 路由: /api/ai/conversations/{conversation_id}, 方法: {'DELETE'}, 名称: delete_conversation
2025-08-03 12:13:53,209 [INFO] 路由: /api/ai/conversations/{conversation_id}/messages, 方法: {'GET'}, 名称: get_conversation_messages
2025-08-03 12:13:53,209 [INFO] 路由: /api/ai/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:13:53,209 [INFO] 路由: /api/ai/settings, 方法: {'GET'}, 名称: ai_settings_get_proxy
2025-08-03 12:13:53,209 [INFO] 路由: /api/ai/settings, 方法: {'GET'}, 名称: get_settings
2025-08-03 12:13:53,209 [INFO] 路由: /api/ai/settings, 方法: {'POST'}, 名称: ai_settings_proxy
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/settings, 方法: {'POST'}, 名称: save_settings
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/system-prompt, 方法: {'POST'}, 名称: update_system_prompt
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/test-connection, 方法: {'POST'}, 名称: test_connection
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/test-webssh, 方法: {'GET'}, 名称: test_webssh
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/webssh-test, 方法: {'GET'}, 名称: webssh_ai_test
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/webssh/analyze-output, 方法: {'POST'}, 名称: webssh_analyze_output
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/webssh/chat, 方法: {'POST'}, 名称: webssh_chat
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/webssh/chat, 方法: {'POST'}, 名称: webssh_chat_simple
2025-08-03 12:13:53,210 [INFO] 路由: /api/ai/webssh/test, 方法: {'GET'}, 名称: webssh_test
2025-08-03 12:13:53,211 [INFO] 路由: /api/ai/webssh/test, 方法: {'GET'}, 名称: webssh_test
2025-08-03 12:13:53,211 [INFO] 路由: /api/api-health, 方法: {'GET'}, 名称: api_health_check
2025-08-03 12:13:53,211 [INFO] 路由: /api/batch-ping, 方法: {'GET'}, 名称: batch_ping_get
2025-08-03 12:13:53,211 [INFO] 路由: /api/batch-ping, 方法: {'POST'}, 名称: batch_ping_post
2025-08-03 12:13:53,211 [INFO] 路由: /api/check-devices, 方法: {'GET'}, 名称: check_all_devices
2025-08-03 12:13:53,212 [INFO] 路由: /api/check-port, 方法: {'GET'}, 名称: check_port
2025-08-03 12:13:53,212 [INFO] 路由: /api/check-tables, 方法: {'GET'}, 名称: check_tables
2025-08-03 12:13:53,212 [INFO] 路由: /api/cli-devices, 方法: {'GET'}, 名称: cli_devices
2025-08-03 12:13:53,212 [INFO] 路由: /api/config-check, 方法: {'GET'}, 名称: config_check
2025-08-03 12:13:53,212 [INFO] 路由: /api/config/, 方法: {'POST'}, 名称: create_configuration
2025-08-03 12:13:53,212 [INFO] 路由: /api/config/, 方法: {'POST'}, 名称: create_configuration
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/config/{config_id}, 方法: {'DELETE'}, 名称: delete_config
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/config/{config_id}, 方法: {'GET'}, 名称: get_config
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/config/{config_id}, 方法: {'PUT'}, 名称: update_config
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/configs, 方法: {'GET'}, 名称: get_configs
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/configs/batch-delete, 方法: {'POST'}, 名称: batch_delete_configs
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/configs/export, 方法: {'POST'}, 名称: export_configs
2025-08-03 12:13:53,213 [INFO] 路由: /api/config/debug/ports, 方法: {'GET'}, 名称: config_debug_ports
2025-08-03 12:13:53,214 [INFO] 路由: /api/config/debug/ports, 方法: {'GET'}, 名称: debug_ports
2025-08-03 12:13:53,214 [INFO] 路由: /api/config/debug/ports, 方法: {'GET'}, 名称: debug_ports
2025-08-03 12:13:53,214 [INFO] 路由: /api/config/debug/test, 方法: {'GET'}, 名称: config_debug_test
2025-08-03 12:13:53,214 [INFO] 路由: /api/config/debug/test, 方法: {'GET'}, 名称: debug_test
2025-08-03 12:13:53,214 [INFO] 路由: /api/config/debug/test, 方法: {'GET'}, 名称: debug_test
2025-08-03 12:13:53,214 [INFO] 路由: /api/config/deploy/{config_id}, 方法: {'POST'}, 名称: deploy_config
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/deployment-logs, 方法: {'DELETE'}, 名称: delete_deployment_logs
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/deployment-logs, 方法: {'GET'}, 名称: get_deployment_logs
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/deployment-logs/clear, 方法: {'POST'}, 名称: delete_deployment_logs
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/deployment-logs/{log_id}, 方法: {'DELETE'}, 名称: delete_deployment_log
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/deployment-logs/{log_id}/delete, 方法: {'POST'}, 名称: delete_deployment_log
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/device/{device_id}, 方法: {'GET'}, 名称: read_device_configurations
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/device/{device_id}, 方法: {'GET'}, 名称: read_device_configurations
2025-08-03 12:13:53,215 [INFO] 路由: /api/config/devices, 方法: {'GET'}, 名称: get_devices
2025-08-03 12:13:53,216 [INFO] 路由: /api/config/devices/count, 方法: {'GET'}, 名称: get_devices_count
2025-08-03 12:13:53,216 [INFO] 路由: /api/config/devices/generate-test-data, 方法: {'POST'}, 名称: generate_test_devices
2025-08-03 12:13:53,216 [INFO] 路由: /api/config/endpoint-list, 方法: {'GET'}, 名称: list_endpoints
2025-08-03 12:13:53,216 [INFO] 路由: /api/config/generate, 方法: {'POST'}, 名称: generate_config
2025-08-03 12:13:53,216 [INFO] 路由: /api/config/generate/{device_id}, 方法: {'POST'}, 名称: generate_configuration
2025-08-03 12:13:53,217 [INFO] 路由: /api/config/generate/{device_id}, 方法: {'POST'}, 名称: generate_configuration
2025-08-03 12:13:53,217 [INFO] 路由: /api/config/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:13:53,217 [INFO] 路由: /api/config/id/{config_id}, 方法: {'DELETE'}, 名称: delete_configuration
2025-08-03 12:13:53,218 [INFO] 路由: /api/config/id/{config_id}, 方法: {'DELETE'}, 名称: delete_configuration
2025-08-03 12:13:53,218 [INFO] 路由: /api/config/id/{config_id}, 方法: {'GET'}, 名称: read_configuration
2025-08-03 12:13:53,218 [INFO] 路由: /api/config/id/{config_id}, 方法: {'GET'}, 名称: read_configuration
2025-08-03 12:13:53,218 [INFO] 路由: /api/config/id/{config_id}, 方法: {'PUT'}, 名称: update_configuration
2025-08-03 12:13:53,218 [INFO] 路由: /api/config/id/{config_id}, 方法: {'PUT'}, 名称: update_configuration
2025-08-03 12:13:53,219 [INFO] 路由: /api/config/ping, 方法: {'GET'}, 名称: ping
2025-08-03 12:13:53,219 [INFO] 路由: /api/config/ports/, 方法: {'POST'}, 名称: create_port_config
2025-08-03 12:13:53,219 [INFO] 路由: /api/config/ports/, 方法: {'POST'}, 名称: create_port_config
2025-08-03 12:13:53,220 [INFO] 路由: /api/config/ports/batch, 方法: {'POST'}, 名称: batch_update_ports
2025-08-03 12:13:53,220 [INFO] 路由: /api/config/ports/batch, 方法: {'POST'}, 名称: batch_update_ports
2025-08-03 12:13:53,220 [INFO] 路由: /api/config/ports/debug/{device_id}, 方法: {'GET'}, 名称: debug_device_ports
2025-08-03 12:13:53,220 [INFO] 路由: /api/config/ports/debug/{device_id}, 方法: {'GET'}, 名称: debug_device_ports
2025-08-03 12:13:53,220 [INFO] 路由: /api/config/ports/device/{device_id}, 方法: {'GET'}, 名称: get_device_ports
2025-08-03 12:13:53,220 [INFO] 路由: /api/config/ports/device/{device_id}, 方法: {'GET'}, 名称: get_device_ports
2025-08-03 12:13:53,221 [INFO] 路由: /api/config/ports/device/{device_id}/fetch, 方法: {'GET'}, 名称: fetch_device_ports
2025-08-03 12:13:53,221 [INFO] 路由: /api/config/ports/device/{device_id}/fetch, 方法: {'GET'}, 名称: fetch_device_ports
2025-08-03 12:13:53,221 [INFO] 路由: /api/config/ports/device/{device_id}/fetch/debug, 方法: {'GET'}, 名称: fetch_device_ports_debug
2025-08-03 12:13:53,222 [INFO] 路由: /api/config/ports/device/{device_id}/fetch/debug, 方法: {'GET'}, 名称: fetch_device_ports_debug
2025-08-03 12:13:53,222 [INFO] 路由: /api/config/ports/device/{device_id}/fetch/debug, 方法: {'GET'}, 名称: fetch_device_ports_debug
2025-08-03 12:13:53,222 [INFO] 路由: /api/config/ports/save/{device_id}, 方法: {'PUT'}, 名称: save_device_ports_config
2025-08-03 12:13:53,222 [INFO] 路由: /api/config/ports/save/{device_id}, 方法: {'PUT'}, 名称: save_device_ports_config
2025-08-03 12:13:53,222 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'DELETE'}, 名称: delete_port_config
2025-08-03 12:13:53,222 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'DELETE'}, 名称: delete_port_config
2025-08-03 12:13:53,223 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'GET'}, 名称: get_port_config
2025-08-03 12:13:53,223 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'GET'}, 名称: get_port_config
2025-08-03 12:13:53,223 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'PUT'}, 名称: update_port_config
2025-08-03 12:13:53,223 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'PUT'}, 名称: update_port_config
2025-08-03 12:13:53,223 [INFO] 路由: /api/config/ports/{port_id}/status, 方法: {'PATCH'}, 名称: update_port_status
2025-08-03 12:13:53,223 [INFO] 路由: /api/config/ports/{port_id}/status, 方法: {'PATCH'}, 名称: update_port_status
2025-08-03 12:13:53,224 [INFO] 路由: /api/config/save, 方法: {'POST'}, 名称: create_config
2025-08-03 12:13:53,224 [INFO] 路由: /api/config/template/, 方法: {'GET'}, 名称: read_templates
2025-08-03 12:13:53,224 [INFO] 路由: /api/config/template/, 方法: {'GET'}, 名称: read_templates
2025-08-03 12:13:53,224 [INFO] 路由: /api/config/template/, 方法: {'POST'}, 名称: create_template
2025-08-03 12:13:53,224 [INFO] 路由: /api/config/template/, 方法: {'POST'}, 名称: create_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/template/{template_id}, 方法: {'DELETE'}, 名称: delete_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/template/{template_id}, 方法: {'DELETE'}, 名称: delete_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/template/{template_id}, 方法: {'GET'}, 名称: read_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/template/{template_id}, 方法: {'GET'}, 名称: read_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/template/{template_id}, 方法: {'PUT'}, 名称: update_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/template/{template_id}, 方法: {'PUT'}, 名称: update_template
2025-08-03 12:13:53,225 [INFO] 路由: /api/config/templates, 方法: {'GET'}, 名称: get_templates
2025-08-03 12:13:53,226 [INFO] 路由: /api/config/templates, 方法: {'POST'}, 名称: create_template
2025-08-03 12:13:53,226 [INFO] 路由: /api/config/test, 方法: {'GET'}, 名称: test_endpoint
2025-08-03 12:13:53,226 [INFO] 路由: /api/configurations/, 方法: {'GET'}, 名称: list_configurations
2025-08-03 12:13:53,226 [INFO] 路由: /api/configurations/, 方法: {'GET'}, 名称: list_configurations
2025-08-03 12:13:53,226 [INFO] 路由: /api/create-device, 方法: {'POST'}, 名称: create_device_direct
2025-08-03 12:13:53,226 [INFO] 路由: /api/datacenter/, 方法: {'GET'}, 名称: get_all_datacenters
2025-08-03 12:13:53,226 [INFO] 路由: /api/datacenter/, 方法: {'POST'}, 名称: create_datacenter
2025-08-03 12:13:53,226 [INFO] 路由: /api/datacenter/debug, 方法: {'GET'}, 名称: debug_datacenter_data
2025-08-03 12:13:53,227 [INFO] 路由: /api/datacenter/statistics, 方法: {'GET'}, 名称: get_datacenter_stats
2025-08-03 12:13:53,227 [INFO] 路由: /api/datacenter/{datacenter_id}, 方法: {'DELETE'}, 名称: delete_datacenter
2025-08-03 12:13:53,227 [INFO] 路由: /api/datacenter/{datacenter_id}, 方法: {'GET'}, 名称: get_datacenter
2025-08-03 12:13:53,227 [INFO] 路由: /api/datacenter/{datacenter_id}, 方法: {'PUT'}, 名称: update_datacenter
2025-08-03 12:13:53,227 [INFO] 路由: /api/db-check, 方法: {'GET'}, 名称: db_check
2025-08-03 12:13:53,227 [INFO] 路由: /api/db-diagnostic, 方法: {'GET'}, 名称: db_diagnostic
2025-08-03 12:13:53,227 [INFO] 路由: /api/debug, 方法: {'GET'}, 名称: debug_info
2025-08-03 12:13:53,227 [INFO] 路由: /api/debug-pagination, 方法: {'GET'}, 名称: debug_pagination
2025-08-03 12:13:53,227 [INFO] 路由: /api/debug/, 方法: {'GET'}, 名称: api_debug
2025-08-03 12:13:53,227 [INFO] 路由: /api/device-groups, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:13:53,228 [INFO] 路由: /api/device-groups, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:13:53,228 [INFO] 路由: /api/device-groups/, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:13:53,228 [INFO] 路由: /api/device-groups/{group_id}, 方法: {'GET'}, 名称: get_device_group
2025-08-03 12:13:53,228 [INFO] 路由: /api/devices, 方法: {'GET'}, 名称: get_all_devices_with_pagination
2025-08-03 12:13:53,228 [INFO] 路由: /api/devices-test, 方法: {'GET'}, 名称: devices_test
2025-08-03 12:13:53,228 [INFO] 路由: /api/devices/, 方法: {'GET'}, 名称: get_all_devices
2025-08-03 12:13:53,228 [INFO] 路由: /api/devices/, 方法: {'POST'}, 名称: create_device
2025-08-03 12:13:53,228 [INFO] 路由: /api/devices/batch-ping, 方法: {'POST'}, 名称: batch_ping
2025-08-03 12:13:53,228 [INFO] 路由: /api/devices/batch_deploy_config, 方法: {'POST'}, 名称: batch_deploy_config_nornir
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/check-unique, 方法: {'GET'}, 名称: check_field_uniqueness
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/deploy_config, 方法: {'POST'}, 名称: deploy_config_to_device
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/device/{device_id}, 方法: {'GET'}, 名称: get_device
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/health, 方法: {'GET'}, 名称: device_health
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/ip-conflicts, 方法: {'GET'}, 名称: detect_ip_conflicts
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/orm-delete/{device_id}, 方法: {'POST'}, 名称: orm_delete_device
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/ping, 方法: {'GET'}, 名称: device_ping
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/ports/netmiko, 方法: {'POST'}, 名称: get_device_ports_netmiko
2025-08-03 12:13:53,229 [INFO] 路由: /api/devices/ports/port_config, 方法: {'POST'}, 名称: get_port_config
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/ports/statistics, 方法: {'GET'}, 名称: get_device_ports_statistics_compat
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/ports/statistics, 方法: {'GET'}, 名称: get_device_ports_statistics_direct
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/purge/{device_id}, 方法: {'POST'}, 名称: purge_device
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/quick-refresh, 方法: {'POST'}, 名称: quick_refresh_devices
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/refresh-status, 方法: {'POST'}, 名称: refresh_devices_status
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/refresh-status, 方法: {'POST'}, 名称: refresh_devices_status
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/remove/{device_id}, 方法: {'POST'}, 名称: remove_device_post
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/safe-delete/{device_id}, 方法: {'POST'}, 名称: safe_delete_device
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/stats, 方法: {'GET'}, 名称: get_device_stats
2025-08-03 12:13:53,230 [INFO] 路由: /api/devices/test/config, 方法: {'POST'}, 名称: test_config_api
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/test/port-vlans, 方法: {'GET'}, 名称: test_port_vlans_api
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/test/vlans, 方法: {'GET'}, 名称: test_vlans_api
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/update-system-info, 方法: {'POST'}, 名称: update_system_info
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/ws/{device_id}, 方法: ['GET'], 名称: webssh_endpoint
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/{device_id}, 方法: {'DELETE'}, 名称: delete_device
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/{device_id}, 方法: {'PUT'}, 名称: update_device
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/{device_id}/config, 方法: {'GET'}, 名称: get_device_config
2025-08-03 12:13:53,231 [INFO] 路由: /api/devices/{device_id}/config, 方法: {'POST'}, 名称: send_device_config
2025-08-03 12:13:53,232 [INFO] 路由: /api/devices/{device_id}/port-vlans, 方法: {'GET'}, 名称: get_device_port_vlans
2025-08-03 12:13:53,232 [INFO] 路由: /api/devices/{device_id}/vlans, 方法: {'GET'}, 名称: get_device_vlans
2025-08-03 12:13:53,232 [INFO] 路由: /api/direct-devices, 方法: {'GET'}, 名称: get_direct_devices
2025-08-03 12:13:53,232 [INFO] 路由: /api/direct-ip-test, 方法: {'GET'}, 名称: direct_ip_test
2025-08-03 12:13:53,232 [INFO] 路由: /api/discovery, 方法: {'POST'}, 名称: start_topology_discovery
2025-08-03 12:13:53,232 [INFO] 路由: /api/discovery/{task_id}, 方法: {'GET'}, 名称: get_discovery_progress
2025-08-03 12:13:53,232 [INFO] 路由: /api/discovery/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_discovery
2025-08-03 12:13:53,232 [INFO] 路由: /api/fixed-devices, 方法: {'GET'}, 名称: get_fixed_devices
2025-08-03 12:13:53,233 [INFO] 路由: /api/force-init-db, 方法: {'POST'}, 名称: force_init_db
2025-08-03 12:13:53,233 [INFO] 路由: /api/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection-results/, 方法: {'GET'}, 名称: get_inspection_results_compat
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection-results/, 方法: {'GET'}, 名称: get_inspection_results_direct
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection/, 方法: {'POST'}, 名称: start_inspection
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection/, 方法: {'POST'}, 名称: start_inspection
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection/archive, 方法: {'POST'}, 名称: archive_inspection_results
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection/cleanup-report-files, 方法: {'POST'}, 名称: cleanup_report_files
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection/clear-logs/{device_id}, 方法: {'POST'}, 名称: clear_device_logs_post
2025-08-03 12:13:53,233 [INFO] 路由: /api/inspection/clear-queue, 方法: {'POST'}, 名称: clear_inspection_queue
2025-08-03 12:13:53,234 [INFO] 路由: /api/inspection/command-templates, 方法: {'GET'}, 名称: list_command_templates
2025-08-03 12:13:53,234 [INFO] 路由: /api/inspection/command-templates, 方法: {'POST'}, 名称: create_command_template
2025-08-03 12:13:53,234 [INFO] 路由: /api/inspection/command-templates, 方法: {'POST'}, 名称: save_command_template
2025-08-03 12:13:53,234 [INFO] 路由: /api/inspection/command-templates-all, 方法: {'GET'}, 名称: get_all_command_templates
2025-08-03 12:13:53,235 [INFO] 路由: /api/inspection/command-templates-list/{manufacturer}, 方法: {'GET'}, 名称: get_manufacturer_templates
2025-08-03 12:13:53,235 [INFO] 路由: /api/inspection/command-templates/{manufacturer}, 方法: {'GET'}, 名称: get_command_template
2025-08-03 12:13:53,235 [INFO] 路由: /api/inspection/command-templates/{template_id}, 方法: {'DELETE'}, 名称: delete_command_template
2025-08-03 12:13:53,235 [INFO] 路由: /api/inspection/command-templates/{template_id}, 方法: {'DELETE'}, 名称: delete_command_template
2025-08-03 12:13:53,235 [INFO] 路由: /api/inspection/command-templates/{template_id}, 方法: {'PUT'}, 名称: update_command_template
2025-08-03 12:13:53,235 [INFO] 路由: /api/inspection/command-templates/{template_id}/content, 方法: {'GET'}, 名称: get_template_content
2025-08-03 12:13:53,236 [INFO] 路由: /api/inspection/compare, 方法: {'GET'}, 名称: compare_inspection_results
2025-08-03 12:13:53,236 [INFO] 路由: /api/inspection/config, 方法: {'POST'}, 名称: update_inspection_config
2025-08-03 12:13:53,236 [INFO] 路由: /api/inspection/device-records/{device_id}, 方法: {'DELETE'}, 名称: delete_device_inspection_records
2025-08-03 12:13:53,236 [INFO] 路由: /api/inspection/devices, 方法: {'GET'}, 名称: get_devices_simple
2025-08-03 12:13:53,236 [INFO] 路由: /api/inspection/email-settings, 方法: {'GET'}, 名称: save_email_settings
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/export, 方法: {'GET'}, 名称: export_inspection
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/file_content, 方法: {'GET'}, 名称: get_inspection_file_content
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/generate-report, 方法: {'POST'}, 名称: generate_inspection_report
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/generate-report-simple, 方法: {'POST'}, 名称: generate_inspection_report_simple
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/history, 方法: {'GET'}, 名称: get_inspection_history
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/history/{record_id}, 方法: {'DELETE'}, 名称: delete_inspection_history_record
2025-08-03 12:13:53,237 [INFO] 路由: /api/inspection/history/{record_id}, 方法: {'GET'}, 名称: get_inspection_history_detail
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/inspection_devices, 方法: {'GET'}, 名称: get_inspection_devices
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/logs/{device_id}, 方法: {'DELETE'}, 名称: clear_device_logs_delete
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/logs/{device_id}, 方法: {'GET'}, 名称: get_device_inspection_logs
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/queue, 方法: {'GET'}, 名称: get_queue_status
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/reinspect/{device_id}, 方法: {'POST'}, 名称: reinspect_device
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/report-files, 方法: {'GET'}, 名称: list_report_files
2025-08-03 12:13:53,238 [INFO] 路由: /api/inspection/report-files/{filename}, 方法: {'DELETE'}, 名称: delete_report_file
2025-08-03 12:13:53,239 [INFO] 路由: /api/inspection/reset-all-inspecting, 方法: {'POST'}, 名称: reset_all_inspecting_status
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/reset-status/{device_id}, 方法: {'POST'}, 名称: reset_inspection_status
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/result, 方法: {'DELETE'}, 名称: delete_inspection_result
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/result/{device_id}, 方法: {'GET'}, 名称: get_inspection_result
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/search, 方法: {'GET'}, 名称: search_inspection_results
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/send-email, 方法: {'POST'}, 名称: send_inspection_email
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/status, 方法: {'GET'}, 名称: get_inspection_devices_status
2025-08-03 12:13:53,240 [INFO] 路由: /api/inspection/test-connectivity, 方法: {'POST'}, 名称: test_device_connectivity
2025-08-03 12:13:53,242 [INFO] 路由: /api/inspection/test-report, 方法: {'POST'}, 名称: test_report_generation
2025-08-03 12:13:53,242 [INFO] 路由: /api/inspection/toggle-logging, 方法: {'GET'}, 名称: get_logging_status
2025-08-03 12:13:53,242 [INFO] 路由: /api/inspection/toggle-logging, 方法: {'POST'}, 名称: toggle_logging
2025-08-03 12:13:53,242 [INFO] 路由: /api/integration/associate, 方法: {'POST'}, 名称: associate_device_with_ip
2025-08-03 12:13:53,242 [INFO] 路由: /api/integration/create-device-from-ip, 方法: {'POST'}, 名称: create_device_from_ip
2025-08-03 12:13:53,242 [INFO] 路由: /api/integration/devices-with-ips, 方法: {'GET'}, 名称: get_devices_with_ips
2025-08-03 12:13:53,242 [INFO] 路由: /api/integration/ips-with-devices, 方法: {'GET'}, 名称: get_ips_with_devices
2025-08-03 12:13:53,243 [INFO] 路由: /api/integration/logs, 方法: {'GET'}, 名称: get_integration_logs
2025-08-03 12:13:53,243 [INFO] 路由: /api/integration/sync-status, 方法: {'POST'}, 名称: sync_device_ip_status
2025-08-03 12:13:53,243 [INFO] 路由: /api/ip-conflicts/comprehensive, 方法: {'GET'}, 名称: comprehensive_conflict_detection
2025-08-03 12:13:53,243 [INFO] 路由: /api/ip-conflicts/conflicts/{conflict_id}/resolve, 方法: {'POST'}, 名称: resolve_conflict_record
2025-08-03 12:13:53,243 [INFO] 路由: /api/ip-conflicts/database-conflicts, 方法: {'GET'}, 名称: detect_database_conflicts
2025-08-03 12:13:53,243 [INFO] 路由: /api/ip-conflicts/device-based-detection, 方法: {'GET'}, 名称: device_based_conflict_detection
2025-08-03 12:13:53,243 [INFO] 路由: /api/ip-conflicts/history, 方法: {'GET'}, 名称: get_detection_history
2025-08-03 12:13:53,243 [INFO] 路由: /api/ip-conflicts/history/{session_id}, 方法: {'GET'}, 名称: get_detection_session_details
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-conflicts/live-verification, 方法: {'POST'}, 名称: verify_device_ips
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-conflicts/resolve-database-conflict, 方法: {'POST'}, 名称: resolve_database_conflict
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-conflicts/summary, 方法: {'GET'}, 名称: get_conflicts_summary
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-locations, 方法: {'POST'}, 名称: batch_ip_locations
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-management/export/, 方法: {'GET'}, 名称: export_ip_addresses
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-management/import/, 方法: {'POST'}, 名称: import_ip_addresses
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-management/ip-addresses/, 方法: {'GET'}, 名称: get_ip_addresses
2025-08-03 12:13:53,244 [INFO] 路由: /api/ip-management/ip-addresses/, 方法: {'POST'}, 名称: create_ip_address
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/ip-addresses/{ip_id}, 方法: {'DELETE'}, 名称: delete_ip_address
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/ip-addresses/{ip_id}, 方法: {'GET'}, 名称: get_ip_address
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/ip-addresses/{ip_id}, 方法: {'PUT'}, 名称: update_ip_address
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/ping/, 方法: {'POST'}, 名称: ping_ip_address
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/scan/, 方法: {'POST'}, 名称: scan_ip_range
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/statistics, 方法: {'GET'}, 名称: get_ip_management_statistics_direct
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/statistics, 方法: {'GET'}, 名称: redirect_ip_management_statistics
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/statistics/, 方法: {'GET'}, 名称: get_ip_statistics
2025-08-03 12:13:53,245 [INFO] 路由: /api/ip-management/subnets/, 方法: {'GET'}, 名称: get_subnets
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/subnets/, 方法: {'POST'}, 名称: create_subnet
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/subnets/{subnet_id}, 方法: {'DELETE'}, 名称: delete_subnet
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/subnets/{subnet_id}, 方法: {'GET'}, 名称: get_subnet
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/subnets/{subnet_id}, 方法: {'PUT'}, 名称: update_subnet
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/subnets/{subnet_id}/usage, 方法: {'GET'}, 名称: get_subnet_usage
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/test-connection/, 方法: {'GET'}, 名称: test_connection
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-management/test/, 方法: {'POST'}, 名称: test_post
2025-08-03 12:13:53,246 [INFO] 路由: /api/ip-test, 方法: {'GET'}, 名称: ip_test
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/activate, 方法: {'POST'}, 名称: activate_license
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/auto-renew, 方法: {'POST'}, 名称: auto_renew_license
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/clear-activation, 方法: {'DELETE'}, 名称: clear_activation_code
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/details, 方法: {'GET'}, 名称: get_license_details
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/machine-code, 方法: {'GET'}, 名称: get_machine_code
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/record-login, 方法: {'POST'}, 名称: record_login
2025-08-03 12:13:53,247 [INFO] 路由: /api/license/status, 方法: {'GET'}, 名称: check_license_status
2025-08-03 12:13:53,248 [INFO] 路由: /api/license/validate-code, 方法: {'POST'}, 名称: validate_activation_code
2025-08-03 12:13:53,248 [INFO] 路由: /api/methods-check, 方法: {'GET'}, 名称: check_methods_support
2025-08-03 12:13:53,248 [INFO] 路由: /api/network/arp-table, 方法: {'GET'}, 名称: get_arp_table
2025-08-03 12:13:53,248 [INFO] 路由: /api/network/batch-collect-background, 方法: {'POST'}, 名称: start_background_batch_collection
2025-08-03 12:13:53,248 [INFO] 路由: /api/network/batch-task/{task_id}, 方法: {'GET'}, 名称: get_batch_task_status
2025-08-03 12:13:53,248 [INFO] 路由: /api/network/batch-task/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_batch_task
2025-08-03 12:13:53,248 [INFO] 路由: /api/network/batch-tasks, 方法: {'GET'}, 名称: list_batch_tasks
2025-08-03 12:13:53,249 [INFO] 路由: /api/network/collect-device-macs, 方法: {'POST'}, 名称: collect_device_mac_addresses
2025-08-03 12:13:53,249 [INFO] 路由: /api/network/collect-mac, 方法: {'POST'}, 名称: collect_mac_address
2025-08-03 12:13:53,249 [INFO] 路由: /api/network/collect-mac-batch, 方法: {'POST'}, 名称: collect_mac_addresses_batch
2025-08-03 12:13:53,250 [INFO] 路由: /api/network/hostname-resolve, 方法: {'POST'}, 名称: resolve_hostname_endpoint
2025-08-03 12:13:53,250 [INFO] 路由: /api/network/multi-detect, 方法: {'POST'}, 名称: multi_layer_detect_endpoint
2025-08-03 12:13:53,250 [INFO] 路由: /api/network/network-scan/{network}, 方法: {'GET'}, 名称: scan_network
2025-08-03 12:13:53,250 [INFO] 路由: /api/network/ping, 方法: {'POST'}, 名称: ping_host_endpoint
2025-08-03 12:13:53,250 [INFO] 路由: /api/pagination-test, 方法: {'GET'}, 名称: pagination_test
2025-08-03 12:13:53,250 [INFO] 路由: /api/ping, 方法: {'GET'}, 名称: ping
2025-08-03 12:13:53,250 [INFO] 路由: /api/ping/platform, 方法: {'GET'}, 名称: get_platform_info
2025-08-03 12:13:53,250 [INFO] 路由: /api/port-monitor-test, 方法: {'GET'}, 名称: port_monitor_test
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/, 方法: {'GET'}, 名称: get_all_racks
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/, 方法: {'POST'}, 名称: create_rack
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/create-with-id, 方法: {'POST'}, 名称: create_rack_with_custom_id
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/debug, 方法: {'POST'}, 名称: debug_post_request
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/debug-stats, 方法: {'GET'}, 名称: debug_rack_stats
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/health-check, 方法: {'GET'}, 名称: health_check
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/routes, 方法: {'GET'}, 名称: get_routes
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/statistics, 方法: {'GET'}, 名称: get_rack_management_statistics_direct
2025-08-03 12:13:53,251 [INFO] 路由: /api/rack-management/statistics, 方法: {'GET'}, 名称: redirect_rack_management_statistics
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/stats, 方法: {'GET'}, 名称: get_rack_stats
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}, 方法: {'DELETE'}, 名称: delete_rack
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}, 方法: {'GET'}, 名称: get_rack_detail
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}, 方法: {'PUT'}, 名称: update_rack
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}/devices, 方法: {'GET'}, 名称: get_rack_devices
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}/devices, 方法: {'POST'}, 名称: add_device_to_rack
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}/devices/{device_id}, 方法: {'DELETE'}, 名称: remove_device_from_rack
2025-08-03 12:13:53,252 [INFO] 路由: /api/rack-management/{rack_id}/devices/{device_id}, 方法: {'PUT'}, 名称: update_rack_device
2025-08-03 12:13:53,252 [INFO] 路由: /api/raw-devices, 方法: {'GET'}, 名称: get_raw_devices
2025-08-03 12:13:53,253 [INFO] 路由: /api/rebuild-devices, 方法: {'POST'}, 名称: rebuild_devices
2025-08-03 12:13:53,253 [INFO] 路由: /api/reset-database, 方法: {'POST'}, 名称: reset_database
2025-08-03 12:13:53,253 [INFO] 路由: /api/router-check, 方法: {'GET'}, 名称: router_check
2025-08-03 12:13:53,253 [INFO] 路由: /api/setup-status, 方法: {'GET'}, 名称: get_setup_status
2025-08-03 12:13:53,253 [INFO] 路由: /api/simple-devices, 方法: {'GET'}, 名称: get_simple_devices
2025-08-03 12:13:53,253 [INFO] 路由: /api/statistics/device-ports, 方法: {'GET'}, 名称: get_device_ports_statistics
2025-08-03 12:13:53,253 [INFO] 路由: /api/statistics/inspection-results, 方法: {'GET'}, 名称: get_inspection_results
2025-08-03 12:13:53,253 [INFO] 路由: /api/statistics/ip-management, 方法: {'GET'}, 名称: get_ip_management_statistics
2025-08-03 12:13:53,254 [INFO] 路由: /api/statistics/ip-management-stats, 方法: {'GET'}, 名称: get_ip_management_statistics_compat
2025-08-03 12:13:53,254 [INFO] 路由: /api/statistics/rack-management, 方法: {'GET'}, 名称: get_rack_management_statistics
2025-08-03 12:13:53,254 [INFO] 路由: /api/statistics/rack-management-stats, 方法: {'GET'}, 名称: get_rack_management_statistics_compat
2025-08-03 12:13:53,254 [INFO] 路由: /api/subnets-direct, 方法: {'GET'}, 名称: subnets_direct
2025-08-03 12:13:53,254 [INFO] 路由: /api/system-info, 方法: {'GET'}, 名称: get_system_info
2025-08-03 12:13:53,254 [INFO] 路由: /api/system/db-info, 方法: {'GET'}, 名称: get_db_info
2025-08-03 12:13:53,254 [INFO] 路由: /api/system/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:13:53,254 [INFO] 路由: /api/system/test-mysql-connection, 方法: {'POST'}, 名称: test_mysql_connection
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/cleanup, 方法: {'POST'}, 名称: cleanup_sessions
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/create-session, 方法: {'POST'}, 名称: create_session
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/diagnostics, 方法: {'GET'}, 名称: webssh_diagnostics
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/page, 方法: {'GET'}, 名称: webssh_page
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/session/{session_id}, 方法: {'DELETE'}, 名称: delete_session
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/sessions, 方法: {'GET'}, 名称: list_sessions
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/test, 方法: {'GET'}, 名称: test_terminal_api
2025-08-03 12:13:53,255 [INFO] 路由: /api/terminal/ws/{session_id}, 方法: ['GET'], 名称: websocket_endpoint
2025-08-03 12:13:53,255 [INFO] 路由: /api/test, 方法: {'GET'}, 名称: test_api
2025-08-03 12:13:53,255 [INFO] 路由: /api/test-post, 方法: {'POST'}, 名称: test_post
2025-08-03 12:13:53,256 [INFO] 路由: /api/test/, 方法: {'GET'}, 名称: api_test
2025-08-03 12:13:53,256 [INFO] 路由: /api/test/device-groups, 方法: {'GET'}, 名称: test_device_groups
2025-08-03 12:13:53,256 [INFO] 路由: /api/test/topology, 方法: {'GET'}, 名称: test_topology
2025-08-03 12:13:53,256 [INFO] 路由: /api/topology, 方法: {'GET'}, 名称: get_topology
2025-08-03 12:13:53,256 [INFO] 路由: /api/topology/, 方法: {'GET'}, 名称: get_topology_data
2025-08-03 12:13:53,256 [INFO] 路由: /api/topology/device-groups, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:13:53,256 [INFO] 路由: /api/topology/discovery, 方法: {'POST'}, 名称: start_discovery
2025-08-03 12:13:53,256 [INFO] 路由: /api/topology/discovery, 方法: {'POST'}, 名称: start_topology_discovery
2025-08-03 12:13:53,256 [INFO] 路由: /api/topology/discovery/{task_id}, 方法: {'GET'}, 名称: get_discovery_progress
2025-08-03 12:13:53,257 [INFO] 路由: /api/topology/discovery/{task_id}, 方法: {'GET'}, 名称: get_discovery_progress
2025-08-03 12:13:53,257 [INFO] 路由: /api/topology/discovery/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_discovery
2025-08-03 12:13:53,257 [INFO] 路由: /api/topology/discovery/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_topology_discovery
2025-08-03 12:13:53,257 [INFO] 路由: /api/topology/update-lldp, 方法: {'POST'}, 名称: update_topology_with_lldp
2025-08-03 12:13:53,257 [INFO] 路由: /api/topology/update-lldp, 方法: {'POST'}, 名称: update_topology_with_lldp
2025-08-03 12:13:53,257 [INFO] 路由: /api/trace-route, 方法: {'GET'}, 名称: trace_route
2025-08-03 12:13:53,257 [INFO] 路由: /api/update-lldp, 方法: {'POST'}, 名称: update_topology_with_lldp
2025-08-03 12:13:53,257 [INFO] 路由: /api/webssh-debug, 方法: {'GET'}, 名称: webssh_debug
2025-08-03 12:13:53,257 [INFO] 路由: /docs, 方法: {'GET', 'HEAD'}, 名称: swagger_ui_html
2025-08-03 12:13:53,257 [INFO] 路由: /docs/oauth2-redirect, 方法: {'GET', 'HEAD'}, 名称: swagger_ui_redirect
2025-08-03 12:13:53,257 [INFO] 路由: /example, 方法: {'GET'}, 名称: example
2025-08-03 12:13:53,257 [INFO] 路由: /license, 方法: {'GET'}, 名称: license_page
2025-08-03 12:13:53,258 [INFO] 路由: /openapi.json, 方法: {'GET', 'HEAD'}, 名称: openapi
2025-08-03 12:13:53,258 [INFO] 路由: /redoc, 方法: {'GET', 'HEAD'}, 名称: redoc_html
2025-08-03 12:13:53,258 [INFO] 路由: /static, 方法: ['GET'], 名称: static
2025-08-03 12:13:53,258 [INFO] 路由: /terminal, 方法: {'GET'}, 名称: get_terminal
2025-08-03 12:13:53,258 [INFO] 路由: /webssh, 方法: {'GET'}, 名称: get_webssh
2025-08-03 12:13:53,258 [INFO] 路由: /webssh-test, 方法: {'GET'}, 名称: webssh_test
2025-08-03 12:13:53,259 [WARNING] 发现可能冲突的路由路径: {'/api/devices/{device_id}', '/api/config/device/{device_id}', '/api/configurations/', '/api/config/deployment-logs', '/api/devices/refresh-status', '/api/config/debug/ports', '/api/ip-management/statistics', '/api/config/ports/device/{device_id}', '/api/inspection/logs/{device_id}', '/api/config/ports/{port_id}/status', '/api/config/ports/device/{device_id}/fetch/debug', '/api/config/config/{config_id}', '/api/config/ports/', '/api/ai/webssh/test', '/api/ip-management/ip-addresses/', '/api/ip-management/ip-addresses/{ip_id}', '/api/topology/discovery/{task_id}', '/api/inspection/command-templates/{template_id}', '/api/devices/', '/api/inspection-results/', '/api/topology/update-lldp', '/api/ip-management/subnets/{subnet_id}', '/api/inspection/history/{record_id}', '/api/inspection/toggle-logging', '/api/config/ports/save/{device_id}', '/api/rack-management/', '/api/rack-management/{rack_id}/devices/{device_id}', '/api/datacenter/', '/api/rack-management/{rack_id}/devices', '/api/ai/settings', '/api/config/templates', '/api/', '/api/config/ports/device/{device_id}/fetch', '/api/rack-management/statistics', '/api/config/ports/debug/{device_id}', '/api/config/debug/test', '/api/devices/ports/statistics', '/api/config/id/{config_id}', '/api/config/ports/batch', '/api/topology/discovery', '/api/device-groups', '/api/config/generate/{device_id}', '/api/config/', '/api/ip-management/subnets/', '/api/devices/{device_id}/config', '/api/config/template/{template_id}', '/api/ai/chat', '/api/config/ports/{port_id}', '/api/inspection/', '/api/rack-management/{rack_id}', '/api/config/template/', '/api/ai/webssh/chat', '/api/inspection/command-templates', '/api/batch-ping', '/api/topology/discovery/{task_id}/cancel', '/api/datacenter/{datacenter_id}'}
2025-08-03 12:13:53,261 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,261 [INFO] SELECT 1
2025-08-03 12:13:53,261 [INFO] [cached since 0.176s ago] {}
2025-08-03 12:13:53,261 [INFO] ROLLBACK
2025-08-03 12:13:53,262 [INFO] 数据库连接正常
2025-08-03 12:13:53,262 [INFO] BEGIN (implicit)
2025-08-03 12:13:53,264 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:13:53,265 [INFO] [generated in 0.00052s] {}
2025-08-03 12:13:53,274 [INFO] 数据库测试: 找到 38 台设备
2025-08-03 12:13:53,275 [INFO] 设备: 测试路由器 (***********) - 类型: router, 厂商: cisco
2025-08-03 12:13:53,275 [INFO] 设备: 测试交换机 (***********) - 类型: switch, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: 服务器1 (************0) - 类型: server, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: AC控制器1 (************) - 类型: ac_controller, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: 防火墙1 (*************) - 类型: firewall, 厂商: h3c
2025-08-03 12:13:53,275 [INFO] 设备: AP1 (***********1) - 类型: access_point, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: SW02 (************02) - 类型: switch, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: SW03 (************03) - 类型: switch, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: test1 (***********2) - 类型: router, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: AP2 (***********9) - 类型: access_point, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: ����ǽ2 (***********4) - 类型: firewall, 厂商: h3c
2025-08-03 12:13:53,275 [INFO] 设备: test13 (***********0) - 类型: router, 厂商: huawei
2025-08-03 12:13:53,275 [INFO] 设备: ����ǽ3 (***********2) - 类型: firewall, 厂商: h3c
2025-08-03 12:13:53,275 [INFO] 设备: ������3 (***********1) - 类型: switch, 厂商: cisco
2025-08-03 12:13:53,276 [INFO] 设备: AP3 (***********7) - 类型: access_point, 厂商: huawei
2025-08-03 12:13:53,276 [INFO] 设备: AC控制器2 (***********8) - 类型: ac_controller, 厂商: huawei
2025-08-03 12:13:53,276 [INFO] 设备: AC控制器3 (***********6) - 类型: ac_controller, 厂商: huawei
2025-08-03 12:13:53,276 [INFO] 设备: Device-192-168-56-1 (************) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,276 [INFO] 设备: Device-192-168-56-10 (************0) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,276 [INFO] 设备: Device-192-168-56-106 (************06) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,276 [INFO] 设备: Device-132-1-1-1 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,276 [INFO] 设备: Device-132-1-1-11 (*********1) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,276 [INFO] 设备: Device-132-1-1-5 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,277 [INFO] 设备: Device-132-1-1-29 (**********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,277 [INFO] 设备: Device-192-168-56-101 (************01) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,277 [INFO] 设备: Device-132-1-1-3 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,277 [INFO] 设备: Device-132-1-1-30 (*********0) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,277 [INFO] 设备: Device-192-168-56-105 (************05) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,277 [INFO] 设备: Device-192-168-56-104 (************04) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,278 [INFO] 设备: Device-192-168-56-107 (************07) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,278 [INFO] 设备: Device-192-168-56-108 (************08) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,278 [INFO] 设备: Device-132-1-1-4 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,278 [INFO] 设备: Device-192-168-56-112 (************12) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,279 [INFO] 设备: Device-192-168-56-109 (************09) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,279 [INFO] 设备: Device-192-168-56-110 (************10) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,279 [INFO] 设备: Device-192-168-56-111 (************11) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,279 [INFO] 设备: Device-192-168-56-11 (************1) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,279 [INFO] 设备: Device-132-1-1-29-1 (**********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:13:53,279 [INFO] ROLLBACK
2025-08-03 12:13:53,280 [INFO] 正在注册端口监控路由...
2025-08-03 12:13:53,280 [INFO] 已注册端口监控路由: 2 个
2025-08-03 12:13:53,280 [INFO]   - /api/check-port [GET]
2025-08-03 12:13:53,280 [INFO]   - /api/trace-route [GET]
2025-08-03 12:15:40,911 [INFO] 使用Nornir进行巡检，无需初始化工作线程池，并发数: 20
2025-08-03 12:15:40,933 [WARNING] PDF生成库未安装: No module named 'reportlab'
2025-08-03 12:15:41,413 [INFO] Python路径: ['G:\\webguanli_sc7-21', 'G:\\webguanli_sc7-21', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312', 'G:\\webguanli_sc7-21\\myvenv', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\win32', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\win32\\lib', 'G:\\webguanli_sc7-21\\myvenv\\Lib\\site-packages\\Pythonwin']
2025-08-03 12:15:41,414 [INFO] Python版本: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-08-03 12:15:41,414 [INFO] 当前工作目录: G:\webguanli_sc7-21
2025-08-03 12:15:41,414 [INFO] 尝试从 netmiko.ssh_exception 导入异常类
2025-08-03 12:15:41,415 [WARNING] 从 netmiko.ssh_exception 导入异常类失败
2025-08-03 12:15:41,415 [INFO] 尝试从 netmiko.exceptions 导入异常类
2025-08-03 12:15:41,415 [INFO] 从 netmiko.exceptions 成功导入异常类
2025-08-03 12:15:41,500 [INFO] WebSSH module initialized
2025-08-03 12:15:41,541 [INFO] === 应用启动 ===
2025-08-03 12:15:41,542 [INFO] Python版本: 3.12.4
2025-08-03 12:15:41,542 [INFO] 操作系统: Windows 11
2025-08-03 12:15:41,542 [INFO] 检查导入的模块...
2025-08-03 12:15:41,542 [INFO] FastAPI版本: 0.115.13
2025-08-03 12:15:41,542 [INFO] SQLAlchemy引擎: Engine(mysql+pymysql://root:***@localhost/netconfig)
2025-08-03 12:15:41,542 [INFO] 模块导入检查完成
2025-08-03 12:15:41,542 [INFO] 正在创建不存在的数据库表...
2025-08-03 12:15:41,556 [INFO] SELECT DATABASE()
2025-08-03 12:15:41,556 [INFO] [raw sql] {}
2025-08-03 12:15:41,557 [INFO] SELECT @@sql_mode
2025-08-03 12:15:41,557 [INFO] [raw sql] {}
2025-08-03 12:15:41,558 [INFO] SELECT @@lower_case_table_names
2025-08-03 12:15:41,558 [INFO] [raw sql] {}
2025-08-03 12:15:41,559 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,559 [INFO] DESCRIBE `netconfig`.`devices`
2025-08-03 12:15:41,559 [INFO] [raw sql] {}
2025-08-03 12:15:41,561 [INFO] DESCRIBE `netconfig`.`configs`
2025-08-03 12:15:41,561 [INFO] [raw sql] {}
2025-08-03 12:15:41,562 [INFO] DESCRIBE `netconfig`.`template_configs`
2025-08-03 12:15:41,562 [INFO] [raw sql] {}
2025-08-03 12:15:41,563 [INFO] DESCRIBE `netconfig`.`config_history`
2025-08-03 12:15:41,564 [INFO] [raw sql] {}
2025-08-03 12:15:41,564 [INFO] DESCRIBE `netconfig`.`command_templates`
2025-08-03 12:15:41,565 [INFO] [raw sql] {}
2025-08-03 12:15:41,566 [INFO] DESCRIBE `netconfig`.`batch_config_history`
2025-08-03 12:15:41,566 [INFO] [raw sql] {}
2025-08-03 12:15:41,567 [INFO] DESCRIBE `netconfig`.`configurations`
2025-08-03 12:15:41,568 [INFO] [raw sql] {}
2025-08-03 12:15:41,569 [INFO] DESCRIBE `netconfig`.`subnets`
2025-08-03 12:15:41,569 [INFO] [raw sql] {}
2025-08-03 12:15:41,570 [INFO] DESCRIBE `netconfig`.`ip_addresses`
2025-08-03 12:15:41,571 [INFO] [raw sql] {}
2025-08-03 12:15:41,572 [INFO] DESCRIBE `netconfig`.`racks`
2025-08-03 12:15:41,572 [INFO] [raw sql] {}
2025-08-03 12:15:41,573 [INFO] DESCRIBE `netconfig`.`rack_devices`
2025-08-03 12:15:41,573 [INFO] [raw sql] {}
2025-08-03 12:15:41,575 [INFO] DESCRIBE `netconfig`.`device_port_configs`
2025-08-03 12:15:41,576 [INFO] [raw sql] {}
2025-08-03 12:15:41,577 [INFO] DESCRIBE `netconfig`.`inspection_results`
2025-08-03 12:15:41,577 [INFO] [raw sql] {}
2025-08-03 12:15:41,578 [INFO] DESCRIBE `netconfig`.`datacenters`
2025-08-03 12:15:41,578 [INFO] [raw sql] {}
2025-08-03 12:15:41,579 [INFO] DESCRIBE `netconfig`.`ai_settings`
2025-08-03 12:15:41,580 [INFO] [raw sql] {}
2025-08-03 12:15:41,581 [INFO] DESCRIBE `netconfig`.`ai_conversations`
2025-08-03 12:15:41,581 [INFO] [raw sql] {}
2025-08-03 12:15:41,582 [INFO] DESCRIBE `netconfig`.`ai_messages`
2025-08-03 12:15:41,582 [INFO] [raw sql] {}
2025-08-03 12:15:41,583 [INFO] DESCRIBE `netconfig`.`ip_conflict_detection_sessions`
2025-08-03 12:15:41,583 [INFO] [raw sql] {}
2025-08-03 12:15:41,583 [INFO] DESCRIBE `netconfig`.`ip_conflict_records`
2025-08-03 12:15:41,583 [INFO] [raw sql] {}
2025-08-03 12:15:41,585 [INFO] DESCRIBE `netconfig`.`ip_conflict_device_details`
2025-08-03 12:15:41,585 [INFO] [raw sql] {}
2025-08-03 12:15:41,586 [INFO] DESCRIBE `netconfig`.`ip_conflict_resolution_logs`
2025-08-03 12:15:41,587 [INFO] [raw sql] {}
2025-08-03 12:15:41,589 [INFO] COMMIT
2025-08-03 12:15:41,589 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,589 [INFO] SHOW FULL TABLES FROM `netconfig`
2025-08-03 12:15:41,589 [INFO] [raw sql] {}
2025-08-03 12:15:41,591 [INFO] ROLLBACK
2025-08-03 12:15:41,591 [INFO] 检查configurations表结构...
2025-08-03 12:15:41,597 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,597 [INFO] SHOW CREATE TABLE `configurations`
2025-08-03 12:15:41,597 [INFO] [raw sql] {}
2025-08-03 12:15:41,600 [INFO] ROLLBACK
2025-08-03 12:15:41,600 [INFO] 完成数据库表创建检查
2025-08-03 12:15:41,738 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,738 [INFO] SELECT 1
2025-08-03 12:15:41,738 [INFO] [generated in 0.00055s] {}
2025-08-03 12:15:41,739 [INFO] ROLLBACK
2025-08-03 12:15:41,739 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,739 [INFO] DESCRIBE `netconfig`.`devices`
2025-08-03 12:15:41,740 [INFO] [raw sql] {}
2025-08-03 12:15:41,742 [INFO] DESCRIBE `netconfig`.`configs`
2025-08-03 12:15:41,742 [INFO] [raw sql] {}
2025-08-03 12:15:41,743 [INFO] DESCRIBE `netconfig`.`template_configs`
2025-08-03 12:15:41,743 [INFO] [raw sql] {}
2025-08-03 12:15:41,744 [INFO] DESCRIBE `netconfig`.`config_history`
2025-08-03 12:15:41,744 [INFO] [raw sql] {}
2025-08-03 12:15:41,745 [INFO] DESCRIBE `netconfig`.`command_templates`
2025-08-03 12:15:41,746 [INFO] [raw sql] {}
2025-08-03 12:15:41,747 [INFO] DESCRIBE `netconfig`.`batch_config_history`
2025-08-03 12:15:41,747 [INFO] [raw sql] {}
2025-08-03 12:15:41,748 [INFO] DESCRIBE `netconfig`.`configurations`
2025-08-03 12:15:41,749 [INFO] [raw sql] {}
2025-08-03 12:15:41,751 [INFO] DESCRIBE `netconfig`.`subnets`
2025-08-03 12:15:41,751 [INFO] [raw sql] {}
2025-08-03 12:15:41,752 [INFO] DESCRIBE `netconfig`.`ip_addresses`
2025-08-03 12:15:41,752 [INFO] [raw sql] {}
2025-08-03 12:15:41,753 [INFO] DESCRIBE `netconfig`.`racks`
2025-08-03 12:15:41,754 [INFO] [raw sql] {}
2025-08-03 12:15:41,756 [INFO] DESCRIBE `netconfig`.`rack_devices`
2025-08-03 12:15:41,756 [INFO] [raw sql] {}
2025-08-03 12:15:41,758 [INFO] DESCRIBE `netconfig`.`device_port_configs`
2025-08-03 12:15:41,758 [INFO] [raw sql] {}
2025-08-03 12:15:41,759 [INFO] DESCRIBE `netconfig`.`inspection_results`
2025-08-03 12:15:41,759 [INFO] [raw sql] {}
2025-08-03 12:15:41,760 [INFO] DESCRIBE `netconfig`.`datacenters`
2025-08-03 12:15:41,760 [INFO] [raw sql] {}
2025-08-03 12:15:41,761 [INFO] DESCRIBE `netconfig`.`ai_settings`
2025-08-03 12:15:41,761 [INFO] [raw sql] {}
2025-08-03 12:15:41,763 [INFO] DESCRIBE `netconfig`.`ai_conversations`
2025-08-03 12:15:41,763 [INFO] [raw sql] {}
2025-08-03 12:15:41,764 [INFO] DESCRIBE `netconfig`.`ai_messages`
2025-08-03 12:15:41,764 [INFO] [raw sql] {}
2025-08-03 12:15:41,765 [INFO] DESCRIBE `netconfig`.`ip_conflict_detection_sessions`
2025-08-03 12:15:41,765 [INFO] [raw sql] {}
2025-08-03 12:15:41,766 [INFO] DESCRIBE `netconfig`.`ip_conflict_records`
2025-08-03 12:15:41,766 [INFO] [raw sql] {}
2025-08-03 12:15:41,768 [INFO] DESCRIBE `netconfig`.`ip_conflict_device_details`
2025-08-03 12:15:41,768 [INFO] [raw sql] {}
2025-08-03 12:15:41,769 [INFO] DESCRIBE `netconfig`.`ip_conflict_resolution_logs`
2025-08-03 12:15:41,769 [INFO] [raw sql] {}
2025-08-03 12:15:41,770 [INFO] COMMIT
2025-08-03 12:15:41,771 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,771 [INFO] 
                CREATE TABLE IF NOT EXISTS inspection_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    device_id INT NOT NULL,
                    inspection_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    status VARCHAR(50) NOT NULL COMMENT '巡检状态: success, failure, timeout',
                    file_path VARCHAR(500) NULL COMMENT '结果文件路径',
                    file_exists BOOLEAN NOT NULL DEFAULT 1 COMMENT '文件是否存在',
                    cpu_usage FLOAT NULL COMMENT 'CPU使用率',
                    memory_usage FLOAT NULL COMMENT '内存使用率',
                    interface_status TEXT NULL COMMENT '接口状态JSON',
                    summary TEXT NULL COMMENT '巡检摘要',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_device_id (device_id),
                    INDEX idx_inspection_time (inspection_time),
                    INDEX idx_status (status),
                    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡检历史记录表';
            
2025-08-03 12:15:41,772 [INFO] [generated in 0.00098s] {}
2025-08-03 12:15:41,776 [INFO] ROLLBACK
2025-08-03 12:15:41,799 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,802 [INFO] SELECT count(*) AS count_1 
FROM (SELECT configs.id AS configs_id, configs.config_name AS configs_config_name, configs.config_content AS configs_config_content, configs.config_type AS configs_config_type, configs.vendor AS configs_vendor, configs.is_deployed AS configs_is_deployed, configs.created_at AS configs_created_at, configs.updated_at AS configs_updated_at 
FROM configs) AS anon_1
2025-08-03 12:15:41,802 [INFO] [generated in 0.00048s] {}
2025-08-03 12:15:41,806 [INFO] ROLLBACK
2025-08-03 12:15:41,806 [INFO] 正在初始化机柜管理模块...
2025-08-03 12:15:41,806 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,806 [INFO] DESCRIBE `netconfig`.`racks`
2025-08-03 12:15:41,807 [INFO] [raw sql] {}
2025-08-03 12:15:41,809 [INFO] DESCRIBE `netconfig`.`rack_devices`
2025-08-03 12:15:41,809 [INFO] [raw sql] {}
2025-08-03 12:15:41,811 [INFO] COMMIT
2025-08-03 12:15:41,812 [INFO] 机柜管理表创建成功
2025-08-03 12:15:41,813 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,816 [INFO] SELECT count(*) AS count_1 
FROM (SELECT racks.id AS racks_id, racks.name AS racks_name, racks.location AS racks_location, racks.datacenter_id AS racks_datacenter_id, racks.total_u AS racks_total_u, racks.used_u AS racks_used_u, racks.width AS racks_width, racks.depth AS racks_depth, racks.temperature AS racks_temperature, racks.humidity AS racks_humidity, racks.power AS racks_power, racks.max_power AS racks_max_power, racks.status AS racks_status, racks.description AS racks_description, racks.created_at AS racks_created_at, racks.updated_at AS racks_updated_at 
FROM racks) AS anon_1
2025-08-03 12:15:41,817 [INFO] [generated in 0.00077s] {}
2025-08-03 12:15:41,820 [INFO] 已存在 3 个机柜，跳过示例数据创建
2025-08-03 12:15:41,820 [INFO] ROLLBACK
2025-08-03 12:15:41,820 [INFO] 机柜管理模块初始化完成
2025-08-03 12:15:41,821 [INFO] 路由: /, 方法: {'GET'}, 名称: root
2025-08-03 12:15:41,822 [INFO] 路由: /api, 方法: {'GET'}, 名称: read_api_root
2025-08-03 12:15:41,822 [INFO] 路由: /api/, 方法: {'GET'}, 名称: api_root
2025-08-03 12:15:41,822 [INFO] 路由: /api/, 方法: {'GET'}, 名称: get_topology_data
2025-08-03 12:15:41,822 [INFO] 路由: /api/, 方法: {'GET'}, 名称: get_users
2025-08-03 12:15:41,823 [INFO] 路由: /api/add-test-devices, 方法: {'POST'}, 名称: add_test_devices
2025-08-03 12:15:41,823 [INFO] 路由: /api/ai-operations/ai-service/status, 方法: {'GET'}, 名称: check_ai_service_status
2025-08-03 12:15:41,824 [INFO] 路由: /api/ai-operations/alerts, 方法: {'GET'}, 名称: get_alerts
2025-08-03 12:15:41,824 [INFO] 路由: /api/ai-operations/alerts/analyze, 方法: {'POST'}, 名称: analyze_alert
2025-08-03 12:15:41,824 [INFO] 路由: /api/ai-operations/analyze-file, 方法: {'POST'}, 名称: analyze_file
2025-08-03 12:15:41,824 [INFO] 路由: /api/ai-operations/anomalies, 方法: {'GET'}, 名称: get_anomalies
2025-08-03 12:15:41,824 [INFO] 路由: /api/ai-operations/anomalies/analyze, 方法: {'POST'}, 名称: analyze_anomaly
2025-08-03 12:15:41,824 [INFO] 路由: /api/ai-operations/automation-tasks, 方法: {'GET'}, 名称: get_automation_tasks
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-operations/device-predictions, 方法: {'GET'}, 名称: get_device_predictions
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-operations/device-predictions/enhanced, 方法: {'GET'}, 名称: get_enhanced_device_predictions
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-operations/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-operations/health-metrics, 方法: {'GET'}, 名称: get_health_metrics
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-operations/knowledge-base/query, 方法: {'POST'}, 名称: knowledge_base_query
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-operations/logs/analyze, 方法: {'POST'}, 名称: analyze_log_file
2025-08-03 12:15:41,825 [INFO] 路由: /api/ai-webssh-test, 方法: {'GET'}, 名称: ai_webssh_test
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai-webssh/analyze-output, 方法: {'POST'}, 名称: webssh_ai_analyze_output
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai-webssh/chat, 方法: {'POST'}, 名称: webssh_ai_chat
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai-webssh/test, 方法: {'GET'}, 名称: webssh_ai_test
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai/chat, 方法: {'POST'}, 名称: ai_chat_proxy
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai/chat, 方法: {'POST'}, 名称: chat
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai/conversations, 方法: {'GET'}, 名称: get_conversations
2025-08-03 12:15:41,826 [INFO] 路由: /api/ai/conversations/{conversation_id}, 方法: {'DELETE'}, 名称: delete_conversation
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/conversations/{conversation_id}/messages, 方法: {'GET'}, 名称: get_conversation_messages
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/settings, 方法: {'GET'}, 名称: ai_settings_get_proxy
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/settings, 方法: {'GET'}, 名称: get_settings
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/settings, 方法: {'POST'}, 名称: ai_settings_proxy
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/settings, 方法: {'POST'}, 名称: save_settings
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/system-prompt, 方法: {'POST'}, 名称: update_system_prompt
2025-08-03 12:15:41,827 [INFO] 路由: /api/ai/test-connection, 方法: {'POST'}, 名称: test_connection
2025-08-03 12:15:41,828 [INFO] 路由: /api/ai/test-webssh, 方法: {'GET'}, 名称: test_webssh
2025-08-03 12:15:41,828 [INFO] 路由: /api/ai/webssh-test, 方法: {'GET'}, 名称: webssh_ai_test
2025-08-03 12:15:41,828 [INFO] 路由: /api/ai/webssh/analyze-output, 方法: {'POST'}, 名称: webssh_analyze_output
2025-08-03 12:15:41,828 [INFO] 路由: /api/ai/webssh/chat, 方法: {'POST'}, 名称: webssh_chat
2025-08-03 12:15:41,828 [INFO] 路由: /api/ai/webssh/chat, 方法: {'POST'}, 名称: webssh_chat_simple
2025-08-03 12:15:41,829 [INFO] 路由: /api/ai/webssh/test, 方法: {'GET'}, 名称: webssh_test
2025-08-03 12:15:41,829 [INFO] 路由: /api/ai/webssh/test, 方法: {'GET'}, 名称: webssh_test
2025-08-03 12:15:41,829 [INFO] 路由: /api/api-health, 方法: {'GET'}, 名称: api_health_check
2025-08-03 12:15:41,829 [INFO] 路由: /api/batch-ping, 方法: {'GET'}, 名称: batch_ping_get
2025-08-03 12:15:41,829 [INFO] 路由: /api/batch-ping, 方法: {'POST'}, 名称: batch_ping_post
2025-08-03 12:15:41,829 [INFO] 路由: /api/check-devices, 方法: {'GET'}, 名称: check_all_devices
2025-08-03 12:15:41,830 [INFO] 路由: /api/check-port, 方法: {'GET'}, 名称: check_port
2025-08-03 12:15:41,830 [INFO] 路由: /api/check-tables, 方法: {'GET'}, 名称: check_tables
2025-08-03 12:15:41,830 [INFO] 路由: /api/cli-devices, 方法: {'GET'}, 名称: cli_devices
2025-08-03 12:15:41,830 [INFO] 路由: /api/config-check, 方法: {'GET'}, 名称: config_check
2025-08-03 12:15:41,830 [INFO] 路由: /api/config/, 方法: {'POST'}, 名称: create_configuration
2025-08-03 12:15:41,830 [INFO] 路由: /api/config/, 方法: {'POST'}, 名称: create_configuration
2025-08-03 12:15:41,831 [INFO] 路由: /api/config/config/{config_id}, 方法: {'DELETE'}, 名称: delete_config
2025-08-03 12:15:41,831 [INFO] 路由: /api/config/config/{config_id}, 方法: {'GET'}, 名称: get_config
2025-08-03 12:15:41,831 [INFO] 路由: /api/config/config/{config_id}, 方法: {'PUT'}, 名称: update_config
2025-08-03 12:15:41,831 [INFO] 路由: /api/config/configs, 方法: {'GET'}, 名称: get_configs
2025-08-03 12:15:41,831 [INFO] 路由: /api/config/configs/batch-delete, 方法: {'POST'}, 名称: batch_delete_configs
2025-08-03 12:15:41,831 [INFO] 路由: /api/config/configs/export, 方法: {'POST'}, 名称: export_configs
2025-08-03 12:15:41,832 [INFO] 路由: /api/config/debug/ports, 方法: {'GET'}, 名称: config_debug_ports
2025-08-03 12:15:41,832 [INFO] 路由: /api/config/debug/ports, 方法: {'GET'}, 名称: debug_ports
2025-08-03 12:15:41,832 [INFO] 路由: /api/config/debug/ports, 方法: {'GET'}, 名称: debug_ports
2025-08-03 12:15:41,832 [INFO] 路由: /api/config/debug/test, 方法: {'GET'}, 名称: config_debug_test
2025-08-03 12:15:41,832 [INFO] 路由: /api/config/debug/test, 方法: {'GET'}, 名称: debug_test
2025-08-03 12:15:41,832 [INFO] 路由: /api/config/debug/test, 方法: {'GET'}, 名称: debug_test
2025-08-03 12:15:41,833 [INFO] 路由: /api/config/deploy/{config_id}, 方法: {'POST'}, 名称: deploy_config
2025-08-03 12:15:41,833 [INFO] 路由: /api/config/deployment-logs, 方法: {'DELETE'}, 名称: delete_deployment_logs
2025-08-03 12:15:41,833 [INFO] 路由: /api/config/deployment-logs, 方法: {'GET'}, 名称: get_deployment_logs
2025-08-03 12:15:41,833 [INFO] 路由: /api/config/deployment-logs/clear, 方法: {'POST'}, 名称: delete_deployment_logs
2025-08-03 12:15:41,833 [INFO] 路由: /api/config/deployment-logs/{log_id}, 方法: {'DELETE'}, 名称: delete_deployment_log
2025-08-03 12:15:41,834 [INFO] 路由: /api/config/deployment-logs/{log_id}/delete, 方法: {'POST'}, 名称: delete_deployment_log
2025-08-03 12:15:41,834 [INFO] 路由: /api/config/device/{device_id}, 方法: {'GET'}, 名称: read_device_configurations
2025-08-03 12:15:41,834 [INFO] 路由: /api/config/device/{device_id}, 方法: {'GET'}, 名称: read_device_configurations
2025-08-03 12:15:41,834 [INFO] 路由: /api/config/devices, 方法: {'GET'}, 名称: get_devices
2025-08-03 12:15:41,834 [INFO] 路由: /api/config/devices/count, 方法: {'GET'}, 名称: get_devices_count
2025-08-03 12:15:41,835 [INFO] 路由: /api/config/devices/generate-test-data, 方法: {'POST'}, 名称: generate_test_devices
2025-08-03 12:15:41,835 [INFO] 路由: /api/config/endpoint-list, 方法: {'GET'}, 名称: list_endpoints
2025-08-03 12:15:41,835 [INFO] 路由: /api/config/generate, 方法: {'POST'}, 名称: generate_config
2025-08-03 12:15:41,835 [INFO] 路由: /api/config/generate/{device_id}, 方法: {'POST'}, 名称: generate_configuration
2025-08-03 12:15:41,835 [INFO] 路由: /api/config/generate/{device_id}, 方法: {'POST'}, 名称: generate_configuration
2025-08-03 12:15:41,836 [INFO] 路由: /api/config/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:15:41,836 [INFO] 路由: /api/config/id/{config_id}, 方法: {'DELETE'}, 名称: delete_configuration
2025-08-03 12:15:41,836 [INFO] 路由: /api/config/id/{config_id}, 方法: {'DELETE'}, 名称: delete_configuration
2025-08-03 12:15:41,836 [INFO] 路由: /api/config/id/{config_id}, 方法: {'GET'}, 名称: read_configuration
2025-08-03 12:15:41,836 [INFO] 路由: /api/config/id/{config_id}, 方法: {'GET'}, 名称: read_configuration
2025-08-03 12:15:41,836 [INFO] 路由: /api/config/id/{config_id}, 方法: {'PUT'}, 名称: update_configuration
2025-08-03 12:15:41,837 [INFO] 路由: /api/config/id/{config_id}, 方法: {'PUT'}, 名称: update_configuration
2025-08-03 12:15:41,837 [INFO] 路由: /api/config/ping, 方法: {'GET'}, 名称: ping
2025-08-03 12:15:41,837 [INFO] 路由: /api/config/ports/, 方法: {'POST'}, 名称: create_port_config
2025-08-03 12:15:41,837 [INFO] 路由: /api/config/ports/, 方法: {'POST'}, 名称: create_port_config
2025-08-03 12:15:41,837 [INFO] 路由: /api/config/ports/batch, 方法: {'POST'}, 名称: batch_update_ports
2025-08-03 12:15:41,837 [INFO] 路由: /api/config/ports/batch, 方法: {'POST'}, 名称: batch_update_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/debug/{device_id}, 方法: {'GET'}, 名称: debug_device_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/debug/{device_id}, 方法: {'GET'}, 名称: debug_device_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}, 方法: {'GET'}, 名称: get_device_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}, 方法: {'GET'}, 名称: get_device_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}/fetch, 方法: {'GET'}, 名称: fetch_device_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}/fetch, 方法: {'GET'}, 名称: fetch_device_ports
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}/fetch/debug, 方法: {'GET'}, 名称: fetch_device_ports_debug
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}/fetch/debug, 方法: {'GET'}, 名称: fetch_device_ports_debug
2025-08-03 12:15:41,838 [INFO] 路由: /api/config/ports/device/{device_id}/fetch/debug, 方法: {'GET'}, 名称: fetch_device_ports_debug
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/save/{device_id}, 方法: {'PUT'}, 名称: save_device_ports_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/save/{device_id}, 方法: {'PUT'}, 名称: save_device_ports_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'DELETE'}, 名称: delete_port_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'DELETE'}, 名称: delete_port_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'GET'}, 名称: get_port_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'GET'}, 名称: get_port_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'PUT'}, 名称: update_port_config
2025-08-03 12:15:41,839 [INFO] 路由: /api/config/ports/{port_id}, 方法: {'PUT'}, 名称: update_port_config
2025-08-03 12:15:41,840 [INFO] 路由: /api/config/ports/{port_id}/status, 方法: {'PATCH'}, 名称: update_port_status
2025-08-03 12:15:41,840 [INFO] 路由: /api/config/ports/{port_id}/status, 方法: {'PATCH'}, 名称: update_port_status
2025-08-03 12:15:41,840 [INFO] 路由: /api/config/save, 方法: {'POST'}, 名称: create_config
2025-08-03 12:15:41,840 [INFO] 路由: /api/config/template/, 方法: {'GET'}, 名称: read_templates
2025-08-03 12:15:41,840 [INFO] 路由: /api/config/template/, 方法: {'GET'}, 名称: read_templates
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/, 方法: {'POST'}, 名称: create_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/, 方法: {'POST'}, 名称: create_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/{template_id}, 方法: {'DELETE'}, 名称: delete_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/{template_id}, 方法: {'DELETE'}, 名称: delete_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/{template_id}, 方法: {'GET'}, 名称: read_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/{template_id}, 方法: {'GET'}, 名称: read_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/{template_id}, 方法: {'PUT'}, 名称: update_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/template/{template_id}, 方法: {'PUT'}, 名称: update_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/templates, 方法: {'GET'}, 名称: get_templates
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/templates, 方法: {'POST'}, 名称: create_template
2025-08-03 12:15:41,841 [INFO] 路由: /api/config/test, 方法: {'GET'}, 名称: test_endpoint
2025-08-03 12:15:41,841 [INFO] 路由: /api/configurations/, 方法: {'GET'}, 名称: list_configurations
2025-08-03 12:15:41,841 [INFO] 路由: /api/configurations/, 方法: {'GET'}, 名称: list_configurations
2025-08-03 12:15:41,841 [INFO] 路由: /api/create-device, 方法: {'POST'}, 名称: create_device_direct
2025-08-03 12:15:41,841 [INFO] 路由: /api/datacenter/, 方法: {'GET'}, 名称: get_all_datacenters
2025-08-03 12:15:41,841 [INFO] 路由: /api/datacenter/, 方法: {'POST'}, 名称: create_datacenter
2025-08-03 12:15:41,841 [INFO] 路由: /api/datacenter/debug, 方法: {'GET'}, 名称: debug_datacenter_data
2025-08-03 12:15:41,843 [INFO] 路由: /api/datacenter/statistics, 方法: {'GET'}, 名称: get_datacenter_stats
2025-08-03 12:15:41,843 [INFO] 路由: /api/datacenter/{datacenter_id}, 方法: {'DELETE'}, 名称: delete_datacenter
2025-08-03 12:15:41,843 [INFO] 路由: /api/datacenter/{datacenter_id}, 方法: {'GET'}, 名称: get_datacenter
2025-08-03 12:15:41,843 [INFO] 路由: /api/datacenter/{datacenter_id}, 方法: {'PUT'}, 名称: update_datacenter
2025-08-03 12:15:41,844 [INFO] 路由: /api/db-check, 方法: {'GET'}, 名称: db_check
2025-08-03 12:15:41,844 [INFO] 路由: /api/db-diagnostic, 方法: {'GET'}, 名称: db_diagnostic
2025-08-03 12:15:41,844 [INFO] 路由: /api/debug, 方法: {'GET'}, 名称: debug_info
2025-08-03 12:15:41,844 [INFO] 路由: /api/debug-pagination, 方法: {'GET'}, 名称: debug_pagination
2025-08-03 12:15:41,844 [INFO] 路由: /api/debug/, 方法: {'GET'}, 名称: api_debug
2025-08-03 12:15:41,844 [INFO] 路由: /api/device-groups, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:15:41,844 [INFO] 路由: /api/device-groups, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:15:41,845 [INFO] 路由: /api/device-groups/, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:15:41,845 [INFO] 路由: /api/device-groups/{group_id}, 方法: {'GET'}, 名称: get_device_group
2025-08-03 12:15:41,845 [INFO] 路由: /api/devices, 方法: {'GET'}, 名称: get_all_devices_with_pagination
2025-08-03 12:15:41,845 [INFO] 路由: /api/devices-test, 方法: {'GET'}, 名称: devices_test
2025-08-03 12:15:41,845 [INFO] 路由: /api/devices/, 方法: {'GET'}, 名称: get_all_devices
2025-08-03 12:15:41,845 [INFO] 路由: /api/devices/, 方法: {'POST'}, 名称: create_device
2025-08-03 12:15:41,845 [INFO] 路由: /api/devices/batch-ping, 方法: {'POST'}, 名称: batch_ping
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/batch_deploy_config, 方法: {'POST'}, 名称: batch_deploy_config_nornir
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/check-unique, 方法: {'GET'}, 名称: check_field_uniqueness
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/deploy_config, 方法: {'POST'}, 名称: deploy_config_to_device
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/device/{device_id}, 方法: {'GET'}, 名称: get_device
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/health, 方法: {'GET'}, 名称: device_health
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/ip-conflicts, 方法: {'GET'}, 名称: detect_ip_conflicts
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/orm-delete/{device_id}, 方法: {'POST'}, 名称: orm_delete_device
2025-08-03 12:15:41,846 [INFO] 路由: /api/devices/ping, 方法: {'GET'}, 名称: device_ping
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/ports/netmiko, 方法: {'POST'}, 名称: get_device_ports_netmiko
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/ports/port_config, 方法: {'POST'}, 名称: get_port_config
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/ports/statistics, 方法: {'GET'}, 名称: get_device_ports_statistics_compat
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/ports/statistics, 方法: {'GET'}, 名称: get_device_ports_statistics_direct
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/purge/{device_id}, 方法: {'POST'}, 名称: purge_device
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/quick-refresh, 方法: {'POST'}, 名称: quick_refresh_devices
2025-08-03 12:15:41,847 [INFO] 路由: /api/devices/refresh-status, 方法: {'POST'}, 名称: refresh_devices_status
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/refresh-status, 方法: {'POST'}, 名称: refresh_devices_status
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/remove/{device_id}, 方法: {'POST'}, 名称: remove_device_post
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/safe-delete/{device_id}, 方法: {'POST'}, 名称: safe_delete_device
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/stats, 方法: {'GET'}, 名称: get_device_stats
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/test/config, 方法: {'POST'}, 名称: test_config_api
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/test/port-vlans, 方法: {'GET'}, 名称: test_port_vlans_api
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/test/vlans, 方法: {'GET'}, 名称: test_vlans_api
2025-08-03 12:15:41,848 [INFO] 路由: /api/devices/update-system-info, 方法: {'POST'}, 名称: update_system_info
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/ws/{device_id}, 方法: ['GET'], 名称: webssh_endpoint
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/{device_id}, 方法: {'DELETE'}, 名称: delete_device
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/{device_id}, 方法: {'PUT'}, 名称: update_device
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/{device_id}/config, 方法: {'GET'}, 名称: get_device_config
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/{device_id}/config, 方法: {'POST'}, 名称: send_device_config
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/{device_id}/port-vlans, 方法: {'GET'}, 名称: get_device_port_vlans
2025-08-03 12:15:41,849 [INFO] 路由: /api/devices/{device_id}/vlans, 方法: {'GET'}, 名称: get_device_vlans
2025-08-03 12:15:41,849 [INFO] 路由: /api/direct-devices, 方法: {'GET'}, 名称: get_direct_devices
2025-08-03 12:15:41,850 [INFO] 路由: /api/direct-ip-test, 方法: {'GET'}, 名称: direct_ip_test
2025-08-03 12:15:41,850 [INFO] 路由: /api/discovery, 方法: {'POST'}, 名称: start_topology_discovery
2025-08-03 12:15:41,850 [INFO] 路由: /api/discovery/{task_id}, 方法: {'GET'}, 名称: get_discovery_progress
2025-08-03 12:15:41,850 [INFO] 路由: /api/discovery/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_discovery
2025-08-03 12:15:41,850 [INFO] 路由: /api/fixed-devices, 方法: {'GET'}, 名称: get_fixed_devices
2025-08-03 12:15:41,850 [INFO] 路由: /api/force-init-db, 方法: {'POST'}, 名称: force_init_db
2025-08-03 12:15:41,850 [INFO] 路由: /api/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:15:41,850 [INFO] 路由: /api/inspection-results/, 方法: {'GET'}, 名称: get_inspection_results_compat
2025-08-03 12:15:41,850 [INFO] 路由: /api/inspection-results/, 方法: {'GET'}, 名称: get_inspection_results_direct
2025-08-03 12:15:41,850 [INFO] 路由: /api/inspection/, 方法: {'POST'}, 名称: start_inspection
2025-08-03 12:15:41,850 [INFO] 路由: /api/inspection/, 方法: {'POST'}, 名称: start_inspection
2025-08-03 12:15:41,850 [INFO] 路由: /api/inspection/archive, 方法: {'POST'}, 名称: archive_inspection_results
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/cleanup-report-files, 方法: {'POST'}, 名称: cleanup_report_files
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/clear-logs/{device_id}, 方法: {'POST'}, 名称: clear_device_logs_post
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/clear-queue, 方法: {'POST'}, 名称: clear_inspection_queue
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/command-templates, 方法: {'GET'}, 名称: list_command_templates
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/command-templates, 方法: {'POST'}, 名称: create_command_template
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/command-templates, 方法: {'POST'}, 名称: save_command_template
2025-08-03 12:15:41,851 [INFO] 路由: /api/inspection/command-templates-all, 方法: {'GET'}, 名称: get_all_command_templates
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/command-templates-list/{manufacturer}, 方法: {'GET'}, 名称: get_manufacturer_templates
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/command-templates/{manufacturer}, 方法: {'GET'}, 名称: get_command_template
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/command-templates/{template_id}, 方法: {'DELETE'}, 名称: delete_command_template
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/command-templates/{template_id}, 方法: {'DELETE'}, 名称: delete_command_template
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/command-templates/{template_id}, 方法: {'PUT'}, 名称: update_command_template
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/command-templates/{template_id}/content, 方法: {'GET'}, 名称: get_template_content
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/compare, 方法: {'GET'}, 名称: compare_inspection_results
2025-08-03 12:15:41,852 [INFO] 路由: /api/inspection/config, 方法: {'POST'}, 名称: update_inspection_config
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/device-records/{device_id}, 方法: {'DELETE'}, 名称: delete_device_inspection_records
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/devices, 方法: {'GET'}, 名称: get_devices_simple
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/email-settings, 方法: {'GET'}, 名称: save_email_settings
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/export, 方法: {'GET'}, 名称: export_inspection
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/file_content, 方法: {'GET'}, 名称: get_inspection_file_content
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/generate-report, 方法: {'POST'}, 名称: generate_inspection_report
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/generate-report-simple, 方法: {'POST'}, 名称: generate_inspection_report_simple
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/history, 方法: {'GET'}, 名称: get_inspection_history
2025-08-03 12:15:41,853 [INFO] 路由: /api/inspection/history/{record_id}, 方法: {'DELETE'}, 名称: delete_inspection_history_record
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/history/{record_id}, 方法: {'GET'}, 名称: get_inspection_history_detail
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/inspection_devices, 方法: {'GET'}, 名称: get_inspection_devices
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/logs/{device_id}, 方法: {'DELETE'}, 名称: clear_device_logs_delete
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/logs/{device_id}, 方法: {'GET'}, 名称: get_device_inspection_logs
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/queue, 方法: {'GET'}, 名称: get_queue_status
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/reinspect/{device_id}, 方法: {'POST'}, 名称: reinspect_device
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/report-files, 方法: {'GET'}, 名称: list_report_files
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/report-files/{filename}, 方法: {'DELETE'}, 名称: delete_report_file
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/reset-all-inspecting, 方法: {'POST'}, 名称: reset_all_inspecting_status
2025-08-03 12:15:41,854 [INFO] 路由: /api/inspection/reset-status/{device_id}, 方法: {'POST'}, 名称: reset_inspection_status
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/result, 方法: {'DELETE'}, 名称: delete_inspection_result
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/result/{device_id}, 方法: {'GET'}, 名称: get_inspection_result
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/search, 方法: {'GET'}, 名称: search_inspection_results
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/send-email, 方法: {'POST'}, 名称: send_inspection_email
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/status, 方法: {'GET'}, 名称: get_inspection_devices_status
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/test-connectivity, 方法: {'POST'}, 名称: test_device_connectivity
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/test-report, 方法: {'POST'}, 名称: test_report_generation
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/toggle-logging, 方法: {'GET'}, 名称: get_logging_status
2025-08-03 12:15:41,855 [INFO] 路由: /api/inspection/toggle-logging, 方法: {'POST'}, 名称: toggle_logging
2025-08-03 12:15:41,856 [INFO] 路由: /api/integration/associate, 方法: {'POST'}, 名称: associate_device_with_ip
2025-08-03 12:15:41,856 [INFO] 路由: /api/integration/create-device-from-ip, 方法: {'POST'}, 名称: create_device_from_ip
2025-08-03 12:15:41,856 [INFO] 路由: /api/integration/devices-with-ips, 方法: {'GET'}, 名称: get_devices_with_ips
2025-08-03 12:15:41,856 [INFO] 路由: /api/integration/ips-with-devices, 方法: {'GET'}, 名称: get_ips_with_devices
2025-08-03 12:15:41,856 [INFO] 路由: /api/integration/logs, 方法: {'GET'}, 名称: get_integration_logs
2025-08-03 12:15:41,856 [INFO] 路由: /api/integration/sync-status, 方法: {'POST'}, 名称: sync_device_ip_status
2025-08-03 12:15:41,856 [INFO] 路由: /api/ip-conflicts/comprehensive, 方法: {'GET'}, 名称: comprehensive_conflict_detection
2025-08-03 12:15:41,856 [INFO] 路由: /api/ip-conflicts/conflicts/{conflict_id}/resolve, 方法: {'POST'}, 名称: resolve_conflict_record
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/database-conflicts, 方法: {'GET'}, 名称: detect_database_conflicts
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/device-based-detection, 方法: {'GET'}, 名称: device_based_conflict_detection
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/history, 方法: {'GET'}, 名称: get_detection_history
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/history/{session_id}, 方法: {'GET'}, 名称: get_detection_session_details
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/live-verification, 方法: {'POST'}, 名称: verify_device_ips
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/resolve-database-conflict, 方法: {'POST'}, 名称: resolve_database_conflict
2025-08-03 12:15:41,857 [INFO] 路由: /api/ip-conflicts/summary, 方法: {'GET'}, 名称: get_conflicts_summary
2025-08-03 12:15:41,858 [INFO] 路由: /api/ip-locations, 方法: {'POST'}, 名称: batch_ip_locations
2025-08-03 12:15:41,858 [INFO] 路由: /api/ip-management/export/, 方法: {'GET'}, 名称: export_ip_addresses
2025-08-03 12:15:41,858 [INFO] 路由: /api/ip-management/import/, 方法: {'POST'}, 名称: import_ip_addresses
2025-08-03 12:15:41,858 [INFO] 路由: /api/ip-management/ip-addresses/, 方法: {'GET'}, 名称: get_ip_addresses
2025-08-03 12:15:41,858 [INFO] 路由: /api/ip-management/ip-addresses/, 方法: {'POST'}, 名称: create_ip_address
2025-08-03 12:15:41,858 [INFO] 路由: /api/ip-management/ip-addresses/{ip_id}, 方法: {'DELETE'}, 名称: delete_ip_address
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/ip-addresses/{ip_id}, 方法: {'GET'}, 名称: get_ip_address
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/ip-addresses/{ip_id}, 方法: {'PUT'}, 名称: update_ip_address
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/ping/, 方法: {'POST'}, 名称: ping_ip_address
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/scan/, 方法: {'POST'}, 名称: scan_ip_range
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/statistics, 方法: {'GET'}, 名称: get_ip_management_statistics_direct
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/statistics, 方法: {'GET'}, 名称: redirect_ip_management_statistics
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/statistics/, 方法: {'GET'}, 名称: get_ip_statistics
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/subnets/, 方法: {'GET'}, 名称: get_subnets
2025-08-03 12:15:41,859 [INFO] 路由: /api/ip-management/subnets/, 方法: {'POST'}, 名称: create_subnet
2025-08-03 12:15:41,860 [INFO] 路由: /api/ip-management/subnets/{subnet_id}, 方法: {'DELETE'}, 名称: delete_subnet
2025-08-03 12:15:41,860 [INFO] 路由: /api/ip-management/subnets/{subnet_id}, 方法: {'GET'}, 名称: get_subnet
2025-08-03 12:15:41,860 [INFO] 路由: /api/ip-management/subnets/{subnet_id}, 方法: {'PUT'}, 名称: update_subnet
2025-08-03 12:15:41,860 [INFO] 路由: /api/ip-management/subnets/{subnet_id}/usage, 方法: {'GET'}, 名称: get_subnet_usage
2025-08-03 12:15:41,860 [INFO] 路由: /api/ip-management/test-connection/, 方法: {'GET'}, 名称: test_connection
2025-08-03 12:15:41,860 [INFO] 路由: /api/ip-management/test/, 方法: {'POST'}, 名称: test_post
2025-08-03 12:15:41,861 [INFO] 路由: /api/ip-test, 方法: {'GET'}, 名称: ip_test
2025-08-03 12:15:41,861 [INFO] 路由: /api/license/activate, 方法: {'POST'}, 名称: activate_license
2025-08-03 12:15:41,861 [INFO] 路由: /api/license/auto-renew, 方法: {'POST'}, 名称: auto_renew_license
2025-08-03 12:15:41,861 [INFO] 路由: /api/license/clear-activation, 方法: {'DELETE'}, 名称: clear_activation_code
2025-08-03 12:15:41,861 [INFO] 路由: /api/license/details, 方法: {'GET'}, 名称: get_license_details
2025-08-03 12:15:41,861 [INFO] 路由: /api/license/machine-code, 方法: {'GET'}, 名称: get_machine_code
2025-08-03 12:15:41,861 [INFO] 路由: /api/license/record-login, 方法: {'POST'}, 名称: record_login
2025-08-03 12:15:41,862 [INFO] 路由: /api/license/status, 方法: {'GET'}, 名称: check_license_status
2025-08-03 12:15:41,862 [INFO] 路由: /api/license/validate-code, 方法: {'POST'}, 名称: validate_activation_code
2025-08-03 12:15:41,862 [INFO] 路由: /api/methods-check, 方法: {'GET'}, 名称: check_methods_support
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/arp-table, 方法: {'GET'}, 名称: get_arp_table
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/batch-collect-background, 方法: {'POST'}, 名称: start_background_batch_collection
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/batch-task/{task_id}, 方法: {'GET'}, 名称: get_batch_task_status
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/batch-task/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_batch_task
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/batch-tasks, 方法: {'GET'}, 名称: list_batch_tasks
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/collect-device-macs, 方法: {'POST'}, 名称: collect_device_mac_addresses
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/collect-mac, 方法: {'POST'}, 名称: collect_mac_address
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/collect-mac-batch, 方法: {'POST'}, 名称: collect_mac_addresses_batch
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/hostname-resolve, 方法: {'POST'}, 名称: resolve_hostname_endpoint
2025-08-03 12:15:41,862 [INFO] 路由: /api/network/multi-detect, 方法: {'POST'}, 名称: multi_layer_detect_endpoint
2025-08-03 12:15:41,863 [INFO] 路由: /api/network/network-scan/{network}, 方法: {'GET'}, 名称: scan_network
2025-08-03 12:15:41,863 [INFO] 路由: /api/network/ping, 方法: {'POST'}, 名称: ping_host_endpoint
2025-08-03 12:15:41,863 [INFO] 路由: /api/pagination-test, 方法: {'GET'}, 名称: pagination_test
2025-08-03 12:15:41,863 [INFO] 路由: /api/ping, 方法: {'GET'}, 名称: ping
2025-08-03 12:15:41,864 [INFO] 路由: /api/ping/platform, 方法: {'GET'}, 名称: get_platform_info
2025-08-03 12:15:41,864 [INFO] 路由: /api/port-monitor-test, 方法: {'GET'}, 名称: port_monitor_test
2025-08-03 12:15:41,864 [INFO] 路由: /api/rack-management/, 方法: {'GET'}, 名称: get_all_racks
2025-08-03 12:15:41,864 [INFO] 路由: /api/rack-management/, 方法: {'POST'}, 名称: create_rack
2025-08-03 12:15:41,864 [INFO] 路由: /api/rack-management/create-with-id, 方法: {'POST'}, 名称: create_rack_with_custom_id
2025-08-03 12:15:41,864 [INFO] 路由: /api/rack-management/debug, 方法: {'POST'}, 名称: debug_post_request
2025-08-03 12:15:41,864 [INFO] 路由: /api/rack-management/debug-stats, 方法: {'GET'}, 名称: debug_rack_stats
2025-08-03 12:15:41,864 [INFO] 路由: /api/rack-management/health-check, 方法: {'GET'}, 名称: health_check
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/routes, 方法: {'GET'}, 名称: get_routes
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/statistics, 方法: {'GET'}, 名称: get_rack_management_statistics_direct
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/statistics, 方法: {'GET'}, 名称: redirect_rack_management_statistics
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/stats, 方法: {'GET'}, 名称: get_rack_stats
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/{rack_id}, 方法: {'DELETE'}, 名称: delete_rack
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/{rack_id}, 方法: {'GET'}, 名称: get_rack_detail
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/{rack_id}, 方法: {'PUT'}, 名称: update_rack
2025-08-03 12:15:41,865 [INFO] 路由: /api/rack-management/{rack_id}/devices, 方法: {'GET'}, 名称: get_rack_devices
2025-08-03 12:15:41,866 [INFO] 路由: /api/rack-management/{rack_id}/devices, 方法: {'POST'}, 名称: add_device_to_rack
2025-08-03 12:15:41,866 [INFO] 路由: /api/rack-management/{rack_id}/devices/{device_id}, 方法: {'DELETE'}, 名称: remove_device_from_rack
2025-08-03 12:15:41,866 [INFO] 路由: /api/rack-management/{rack_id}/devices/{device_id}, 方法: {'PUT'}, 名称: update_rack_device
2025-08-03 12:15:41,866 [INFO] 路由: /api/raw-devices, 方法: {'GET'}, 名称: get_raw_devices
2025-08-03 12:15:41,866 [INFO] 路由: /api/rebuild-devices, 方法: {'POST'}, 名称: rebuild_devices
2025-08-03 12:15:41,867 [INFO] 路由: /api/reset-database, 方法: {'POST'}, 名称: reset_database
2025-08-03 12:15:41,867 [INFO] 路由: /api/router-check, 方法: {'GET'}, 名称: router_check
2025-08-03 12:15:41,867 [INFO] 路由: /api/setup-status, 方法: {'GET'}, 名称: get_setup_status
2025-08-03 12:15:41,867 [INFO] 路由: /api/simple-devices, 方法: {'GET'}, 名称: get_simple_devices
2025-08-03 12:15:41,867 [INFO] 路由: /api/statistics/device-ports, 方法: {'GET'}, 名称: get_device_ports_statistics
2025-08-03 12:15:41,867 [INFO] 路由: /api/statistics/inspection-results, 方法: {'GET'}, 名称: get_inspection_results
2025-08-03 12:15:41,867 [INFO] 路由: /api/statistics/ip-management, 方法: {'GET'}, 名称: get_ip_management_statistics
2025-08-03 12:15:41,867 [INFO] 路由: /api/statistics/ip-management-stats, 方法: {'GET'}, 名称: get_ip_management_statistics_compat
2025-08-03 12:15:41,868 [INFO] 路由: /api/statistics/rack-management, 方法: {'GET'}, 名称: get_rack_management_statistics
2025-08-03 12:15:41,868 [INFO] 路由: /api/statistics/rack-management-stats, 方法: {'GET'}, 名称: get_rack_management_statistics_compat
2025-08-03 12:15:41,868 [INFO] 路由: /api/subnets-direct, 方法: {'GET'}, 名称: subnets_direct
2025-08-03 12:15:41,868 [INFO] 路由: /api/system-info, 方法: {'GET'}, 名称: get_system_info
2025-08-03 12:15:41,868 [INFO] 路由: /api/system/db-info, 方法: {'GET'}, 名称: get_db_info
2025-08-03 12:15:41,868 [INFO] 路由: /api/system/health, 方法: {'GET'}, 名称: health_check
2025-08-03 12:15:41,868 [INFO] 路由: /api/system/test-mysql-connection, 方法: {'POST'}, 名称: test_mysql_connection
2025-08-03 12:15:41,868 [INFO] 路由: /api/terminal/cleanup, 方法: {'POST'}, 名称: cleanup_sessions
2025-08-03 12:15:41,868 [INFO] 路由: /api/terminal/create-session, 方法: {'POST'}, 名称: create_session
2025-08-03 12:15:41,869 [INFO] 路由: /api/terminal/diagnostics, 方法: {'GET'}, 名称: webssh_diagnostics
2025-08-03 12:15:41,869 [INFO] 路由: /api/terminal/page, 方法: {'GET'}, 名称: webssh_page
2025-08-03 12:15:41,869 [INFO] 路由: /api/terminal/session/{session_id}, 方法: {'DELETE'}, 名称: delete_session
2025-08-03 12:15:41,869 [INFO] 路由: /api/terminal/sessions, 方法: {'GET'}, 名称: list_sessions
2025-08-03 12:15:41,869 [INFO] 路由: /api/terminal/test, 方法: {'GET'}, 名称: test_terminal_api
2025-08-03 12:15:41,869 [INFO] 路由: /api/terminal/ws/{session_id}, 方法: ['GET'], 名称: websocket_endpoint
2025-08-03 12:15:41,870 [INFO] 路由: /api/test, 方法: {'GET'}, 名称: test_api
2025-08-03 12:15:41,870 [INFO] 路由: /api/test-post, 方法: {'POST'}, 名称: test_post
2025-08-03 12:15:41,870 [INFO] 路由: /api/test/, 方法: {'GET'}, 名称: api_test
2025-08-03 12:15:41,870 [INFO] 路由: /api/test/device-groups, 方法: {'GET'}, 名称: test_device_groups
2025-08-03 12:15:41,870 [INFO] 路由: /api/test/topology, 方法: {'GET'}, 名称: test_topology
2025-08-03 12:15:41,870 [INFO] 路由: /api/topology, 方法: {'GET'}, 名称: get_topology
2025-08-03 12:15:41,871 [INFO] 路由: /api/topology/, 方法: {'GET'}, 名称: get_topology_data
2025-08-03 12:15:41,871 [INFO] 路由: /api/topology/device-groups, 方法: {'GET'}, 名称: get_device_groups
2025-08-03 12:15:41,871 [INFO] 路由: /api/topology/discovery, 方法: {'POST'}, 名称: start_discovery
2025-08-03 12:15:41,871 [INFO] 路由: /api/topology/discovery, 方法: {'POST'}, 名称: start_topology_discovery
2025-08-03 12:15:41,871 [INFO] 路由: /api/topology/discovery/{task_id}, 方法: {'GET'}, 名称: get_discovery_progress
2025-08-03 12:15:41,871 [INFO] 路由: /api/topology/discovery/{task_id}, 方法: {'GET'}, 名称: get_discovery_progress
2025-08-03 12:15:41,872 [INFO] 路由: /api/topology/discovery/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_discovery
2025-08-03 12:15:41,872 [INFO] 路由: /api/topology/discovery/{task_id}/cancel, 方法: {'POST'}, 名称: cancel_topology_discovery
2025-08-03 12:15:41,872 [INFO] 路由: /api/topology/update-lldp, 方法: {'POST'}, 名称: update_topology_with_lldp
2025-08-03 12:15:41,872 [INFO] 路由: /api/topology/update-lldp, 方法: {'POST'}, 名称: update_topology_with_lldp
2025-08-03 12:15:41,872 [INFO] 路由: /api/trace-route, 方法: {'GET'}, 名称: trace_route
2025-08-03 12:15:41,872 [INFO] 路由: /api/update-lldp, 方法: {'POST'}, 名称: update_topology_with_lldp
2025-08-03 12:15:41,872 [INFO] 路由: /api/webssh-debug, 方法: {'GET'}, 名称: webssh_debug
2025-08-03 12:15:41,873 [INFO] 路由: /docs, 方法: {'HEAD', 'GET'}, 名称: swagger_ui_html
2025-08-03 12:15:41,873 [INFO] 路由: /docs/oauth2-redirect, 方法: {'HEAD', 'GET'}, 名称: swagger_ui_redirect
2025-08-03 12:15:41,873 [INFO] 路由: /example, 方法: {'GET'}, 名称: example
2025-08-03 12:15:41,873 [INFO] 路由: /license, 方法: {'GET'}, 名称: license_page
2025-08-03 12:15:41,873 [INFO] 路由: /openapi.json, 方法: {'HEAD', 'GET'}, 名称: openapi
2025-08-03 12:15:41,874 [INFO] 路由: /redoc, 方法: {'HEAD', 'GET'}, 名称: redoc_html
2025-08-03 12:15:41,874 [INFO] 路由: /static, 方法: ['GET'], 名称: static
2025-08-03 12:15:41,874 [INFO] 路由: /terminal, 方法: {'GET'}, 名称: get_terminal
2025-08-03 12:15:41,875 [INFO] 路由: /webssh, 方法: {'GET'}, 名称: get_webssh
2025-08-03 12:15:41,875 [INFO] 路由: /webssh-test, 方法: {'GET'}, 名称: webssh_test
2025-08-03 12:15:41,876 [WARNING] 发现可能冲突的路由路径: {'/api/config/ports/device/{device_id}/fetch', '/api/config/ports/device/{device_id}', '/api/datacenter/', '/api/devices/{device_id}/config', '/api/datacenter/{datacenter_id}', '/api/config/device/{device_id}', '/api/config/deployment-logs', '/api/ai/chat', '/api/topology/update-lldp', '/api/config/id/{config_id}', '/api/', '/api/ai/webssh/test', '/api/ip-management/ip-addresses/{ip_id}', '/api/inspection-results/', '/api/config/ports/batch', '/api/devices/ports/statistics', '/api/config/debug/test', '/api/config/ports/{port_id}/status', '/api/inspection/', '/api/ip-management/subnets/{subnet_id}', '/api/config/templates', '/api/inspection/command-templates/{template_id}', '/api/config/ports/debug/{device_id}', '/api/ip-management/ip-addresses/', '/api/config/ports/{port_id}', '/api/rack-management/statistics', '/api/config/template/{template_id}', '/api/configurations/', '/api/config/ports/device/{device_id}/fetch/debug', '/api/inspection/command-templates', '/api/topology/discovery/{task_id}', '/api/config/template/', '/api/ai/settings', '/api/batch-ping', '/api/ai/webssh/chat', '/api/inspection/history/{record_id}', '/api/rack-management/', '/api/config/', '/api/devices/', '/api/inspection/logs/{device_id}', '/api/ip-management/statistics', '/api/config/generate/{device_id}', '/api/device-groups', '/api/devices/refresh-status', '/api/inspection/toggle-logging', '/api/rack-management/{rack_id}/devices', '/api/rack-management/{rack_id}', '/api/ip-management/subnets/', '/api/config/ports/save/{device_id}', '/api/topology/discovery', '/api/devices/{device_id}', '/api/config/debug/ports', '/api/config/config/{config_id}', '/api/rack-management/{rack_id}/devices/{device_id}', '/api/config/ports/', '/api/topology/discovery/{task_id}/cancel'}
2025-08-03 12:15:41,877 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,877 [INFO] SELECT 1
2025-08-03 12:15:41,878 [INFO] [cached since 0.14s ago] {}
2025-08-03 12:15:41,878 [INFO] ROLLBACK
2025-08-03 12:15:41,879 [INFO] 数据库连接正常
2025-08-03 12:15:41,879 [INFO] BEGIN (implicit)
2025-08-03 12:15:41,883 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:41,884 [INFO] [generated in 0.00105s] {}
2025-08-03 12:15:41,886 [INFO] 数据库测试: 找到 38 台设备
2025-08-03 12:15:41,886 [INFO] 设备: 测试路由器 (***********) - 类型: router, 厂商: cisco
2025-08-03 12:15:41,886 [INFO] 设备: 测试交换机 (***********) - 类型: switch, 厂商: huawei
2025-08-03 12:15:41,887 [INFO] 设备: 服务器1 (************0) - 类型: server, 厂商: huawei
2025-08-03 12:15:41,887 [INFO] 设备: AC控制器1 (************) - 类型: ac_controller, 厂商: huawei
2025-08-03 12:15:41,887 [INFO] 设备: 防火墙1 (*************) - 类型: firewall, 厂商: h3c
2025-08-03 12:15:41,887 [INFO] 设备: AP1 (***********1) - 类型: access_point, 厂商: huawei
2025-08-03 12:15:41,887 [INFO] 设备: SW02 (************02) - 类型: switch, 厂商: huawei
2025-08-03 12:15:41,887 [INFO] 设备: SW03 (************03) - 类型: switch, 厂商: huawei
2025-08-03 12:15:41,888 [INFO] 设备: test1 (***********2) - 类型: router, 厂商: huawei
2025-08-03 12:15:41,888 [INFO] 设备: AP2 (***********9) - 类型: access_point, 厂商: huawei
2025-08-03 12:15:41,888 [INFO] 设备: ����ǽ2 (***********4) - 类型: firewall, 厂商: h3c
2025-08-03 12:15:41,888 [INFO] 设备: test13 (***********0) - 类型: router, 厂商: huawei
2025-08-03 12:15:41,888 [INFO] 设备: ����ǽ3 (***********2) - 类型: firewall, 厂商: h3c
2025-08-03 12:15:41,888 [INFO] 设备: ������3 (***********1) - 类型: switch, 厂商: cisco
2025-08-03 12:15:41,888 [INFO] 设备: AP3 (***********7) - 类型: access_point, 厂商: huawei
2025-08-03 12:15:41,889 [INFO] 设备: AC控制器2 (***********8) - 类型: ac_controller, 厂商: huawei
2025-08-03 12:15:41,889 [INFO] 设备: AC控制器3 (***********6) - 类型: ac_controller, 厂商: huawei
2025-08-03 12:15:41,889 [INFO] 设备: Device-192-168-56-1 (************) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-192-168-56-10 (************0) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-192-168-56-106 (************06) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-132-1-1-1 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-132-1-1-11 (*********1) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-132-1-1-5 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-132-1-1-29 (**********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-192-168-56-101 (************01) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,889 [INFO] 设备: Device-132-1-1-3 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-132-1-1-30 (*********0) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-105 (************05) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-104 (************04) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-107 (************07) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-108 (************08) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-132-1-1-4 (*********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-112 (************12) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-109 (************09) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,890 [INFO] 设备: Device-192-168-56-110 (************10) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,891 [INFO] 设备: Device-192-168-56-111 (************11) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,891 [INFO] 设备: Device-192-168-56-11 (************1) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,891 [INFO] 设备: Device-132-1-1-29-1 (**********) - 类型: unknown, 厂商: Unknown
2025-08-03 12:15:41,891 [INFO] ROLLBACK
2025-08-03 12:15:41,892 [INFO] 正在注册端口监控路由...
2025-08-03 12:15:41,893 [INFO] 已注册端口监控路由: 2 个
2025-08-03 12:15:41,893 [INFO]   - /api/check-port [GET]
2025-08-03 12:15:41,893 [INFO]   - /api/trace-route [GET]
2025-08-03 12:15:44,671 [INFO] [**********.671209] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:44,671 [INFO] [**********.671209] 开始请求: GET http://localhost:5888/api/ai/health
2025-08-03 12:15:44,844 [INFO] [**********.844423] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:15:45,003 [INFO] [**********.671209] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.3319s
2025-08-03 12:15:45,004 [INFO] [**********.671209] 完成请求: GET http://localhost:5888/api/ai/health - 状态码: 200 - 耗时: 0.3329s
2025-08-03 12:15:45,005 [INFO] [**********.844423] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.1607s
2025-08-03 12:15:45,006 [INFO] [**********.006198] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:45,006 [INFO] [**********.006716] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:15:45,168 [INFO] [**********.006198] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1627s
2025-08-03 12:15:45,169 [INFO] [**********.006716] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.1627s
2025-08-03 12:15:45,170 [INFO] [**********.170465] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:15:45,172 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,173 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:15:45,174 [INFO] [generated in 0.00060s] {'param_1': 1}
2025-08-03 12:15:45,179 [INFO] [**********.179172] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:45,180 [INFO] ROLLBACK
2025-08-03 12:15:45,338 [INFO] [**********.179172] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1596s
2025-08-03 12:15:45,339 [INFO] [**********.170465] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.1693s
2025-08-03 12:15:45,341 [INFO] [**********.34182] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:15:45,637 [INFO] [**********.34182] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.2959s
2025-08-03 12:15:45,827 [INFO] [**********.827974] 开始请求: GET http://localhost:5888/api/devices/stats
2025-08-03 12:15:45,828 [INFO] [**********.828956] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=50
2025-08-03 12:15:45,831 [INFO] [**********.831055] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=20
2025-08-03 12:15:45,831 [INFO] [**********.831055] 开始请求: GET http://localhost:5888/api/devices/ports/statistics
2025-08-03 12:15:45,831 [INFO] [**********.831567] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:45,834 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,841 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:45,842 [INFO] [generated in 0.00164s] {}
2025-08-03 12:15:45,843 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,844 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,844 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,844 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,845 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:45,845 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:45,846 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:45,852 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:45,853 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:45,854 [INFO] [cached since 0.01273s ago] {}
2025-08-03 12:15:45,855 [INFO] [cached since 3.972s ago] {}
2025-08-03 12:15:45,855 [INFO] [cached since 0.01461s ago] {}
2025-08-03 12:15:45,856 [INFO] [generated in 0.00441s] {'device_type_1_1': 'switch', 'device_type_1_2': 'router', 'device_type_1_3': '交换机', 'device_type_1_4': '路由器'}
2025-08-03 12:15:45,857 [INFO] [cached since 0.01583s ago] {}
2025-08-03 12:15:45,860 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:45,863 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:45,863 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:45,864 [INFO] [cached since 0.01269s ago] {'device_type_1_1': 'firewall', 'device_type_1_2': 'security', 'device_type_1_3': '防火墙', 'device_type_1_4': '安全设备'}
2025-08-03 12:15:45,866 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:45,867 [INFO] [generated in 0.00405s] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:45,867 [INFO] [cached since 0.004864s ago] {'param_1': 0, 'param_2': 50}
2025-08-03 12:15:45,869 [INFO] ROLLBACK
2025-08-03 12:15:45,870 [INFO] [cached since 0.007381s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:45,877 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.is_active = true) AS anon_1
2025-08-03 12:15:45,879 [INFO] [generated in 0.00259s] {}
2025-08-03 12:15:45,883 [INFO] [**********.831055] 完成请求: GET http://localhost:5888/api/devices/ports/statistics - 状态码: 200 - 耗时: 0.0528s
2025-08-03 12:15:45,889 [INFO] SELECT devices.manufacturer AS devices_manufacturer, count(devices.id) AS count 
FROM devices 
WHERE devices.manufacturer IS NOT NULL AND devices.manufacturer != %(manufacturer_1)s GROUP BY devices.manufacturer
2025-08-03 12:15:45,897 [INFO] [generated in 0.00881s] {'manufacturer_1': ''}
2025-08-03 12:15:45,915 [INFO] [**********.915859] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:15:45,919 [INFO] ROLLBACK
2025-08-03 12:15:45,919 [INFO] ROLLBACK
2025-08-03 12:15:45,921 [INFO] ROLLBACK
2025-08-03 12:15:45,923 [INFO] [**********.915859] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0067s
2025-08-03 12:15:45,926 [INFO] [**********.831055] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0959s
2025-08-03 12:15:45,926 [INFO] ROLLBACK
2025-08-03 12:15:45,927 [INFO] [**********.831567] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0963s
2025-08-03 12:15:45,928 [INFO] [**********.828956] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=50 - 状态码: 200 - 耗时: 0.1000s
2025-08-03 12:15:45,931 [INFO] [**********.931351] 开始请求: GET http://localhost:5888/api/ip-management/statistics
2025-08-03 12:15:45,933 [INFO] [**********.827974] 完成请求: GET http://localhost:5888/api/devices/stats - 状态码: 200 - 耗时: 0.1057s
2025-08-03 12:15:45,940 [INFO] [**********.940447] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=1
2025-08-03 12:15:45,941 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,945 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:45,948 [INFO] [**********.948896] 开始请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20
2025-08-03 12:15:45,949 [INFO] [cached since 4.067s ago] {}
2025-08-03 12:15:45,954 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,960 [INFO] ROLLBACK
2025-08-03 12:15:45,962 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:45,963 [INFO] BEGIN (implicit)
2025-08-03 12:15:45,970 [INFO] [**********.931351] 完成请求: GET http://localhost:5888/api/ip-management/statistics - 状态码: 200 - 耗时: 0.0390s
2025-08-03 12:15:46,002 [INFO] [**********.002814] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:45,965 [INFO] [cached since 0.1238s ago] {}
2025-08-03 12:15:45,987 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:46,027 [INFO] [**********.027788] 开始请求: GET http://localhost:5888/api/rack-management/statistics
2025-08-03 12:15:46,031 [INFO] BEGIN (implicit)
2025-08-03 12:15:46,034 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:46,036 [INFO] [cached since 0.1948s ago] {}
2025-08-03 12:15:46,050 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:46,053 [INFO] BEGIN (implicit)
2025-08-03 12:15:46,053 [INFO] [generated in 0.06626s] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:46,053 [INFO] [cached since 0.191s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:46,056 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:46,057 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:46,059 [INFO] [cached since 4.177s ago] {}
2025-08-03 12:15:46,060 [INFO] [cached since 0.1977s ago] {'param_1': 0, 'param_2': 1}
2025-08-03 12:15:46,067 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:46,070 [INFO] [cached since 0.2298s ago] {}
2025-08-03 12:15:46,080 [INFO] ROLLBACK
2025-08-03 12:15:46,088 [INFO] [**********.002814] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0857s
2025-08-03 12:15:46,088 [INFO] ROLLBACK
2025-08-03 12:15:46,099 [INFO] [**********.027788] 完成请求: GET http://localhost:5888/api/rack-management/statistics - 状态码: 200 - 耗时: 0.0717s
2025-08-03 12:15:46,102 [INFO] ROLLBACK
2025-08-03 12:15:46,109 [INFO] ROLLBACK
2025-08-03 12:15:46,111 [INFO] [**********.948896] 完成请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.1622s
2025-08-03 12:15:46,113 [INFO] [**********.940447] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=1 - 状态码: 200 - 耗时: 0.1728s
2025-08-03 12:15:46,188 [INFO] [**********.187893] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:15:46,193 [INFO] BEGIN (implicit)
2025-08-03 12:15:46,194 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:15:46,194 [INFO] [cached since 1.021s ago] {'param_1': 1}
2025-08-03 12:15:46,198 [INFO] ROLLBACK
2025-08-03 12:15:46,201 [INFO] [**********.187893] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.0130s
2025-08-03 12:15:48,429 [INFO] [**********.429611] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:15:48,430 [INFO] [**********.429611] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:15:48,434 [INFO] [**********.434473] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:48,595 [INFO] [**********.434473] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1607s
2025-08-03 12:15:48,598 [INFO] [**********.598789] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:15:48,758 [INFO] [**********.598789] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.1595s
2025-08-03 12:15:48,840 [INFO] [**********.840568] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:49,003 [INFO] [**********.840568] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1631s
2025-08-03 12:15:49,005 [INFO] [**********.005195] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:49,006 [INFO] [**********.006238] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=50
2025-08-03 12:15:49,006 [INFO] [**********.006766] 开始请求: GET http://localhost:5888/api/devices/stats
2025-08-03 12:15:49,007 [INFO] [**********.007819] 开始请求: GET http://localhost:5888/api/devices/ports/statistics
2025-08-03 12:15:49,008 [INFO] [**********.008913] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=20
2025-08-03 12:15:49,012 [INFO] [**********.012042] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:15:49,017 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,018 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,025 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,030 [INFO] [**********.012042] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0180s
2025-08-03 12:15:49,027 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,028 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,030 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,031 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:49,031 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,032 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,033 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,034 [INFO] [cached since 3.194s ago] {}
2025-08-03 12:15:49,035 [INFO] [cached since 7.153s ago] {}
2025-08-03 12:15:49,035 [INFO] [cached since 3.195s ago] {}
2025-08-03 12:15:49,036 [INFO] [cached since 3.195s ago] {}
2025-08-03 12:15:49,036 [INFO] [cached since 3.196s ago] {}
2025-08-03 12:15:49,039 [INFO] [**********.039003] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=1
2025-08-03 12:15:49,040 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:49,042 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:49,045 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:49,046 [INFO] [cached since 3.183s ago] {'param_1': 0, 'param_2': 50}
2025-08-03 12:15:49,046 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:49,048 [INFO] [cached since 3.186s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:49,051 [INFO] [cached since 3.199s ago] {'device_type_1_1': 'switch', 'device_type_1_2': 'router', 'device_type_1_3': '交换机', 'device_type_1_4': '路由器'}
2025-08-03 12:15:49,213 [INFO] [cached since 3.35s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:49,215 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,215 [INFO] ROLLBACK
2025-08-03 12:15:49,217 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,220 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:49,223 [INFO] [cached since 3.382s ago] {}
2025-08-03 12:15:49,224 [INFO] [cached since 3.372s ago] {'device_type_1_1': 'firewall', 'device_type_1_2': 'security', 'device_type_1_3': '防火墙', 'device_type_1_4': '安全设备'}
2025-08-03 12:15:49,230 [INFO] [**********.007819] 完成请求: GET http://localhost:5888/api/devices/ports/statistics - 状态码: 200 - 耗时: 0.2221s
2025-08-03 12:15:49,232 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.is_active = true) AS anon_1
2025-08-03 12:15:49,235 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:49,237 [INFO] [cached since 3.359s ago] {}
2025-08-03 12:15:49,239 [INFO] [cached since 3.376s ago] {'param_1': 0, 'param_2': 1}
2025-08-03 12:15:49,246 [INFO] [**********.246701] 开始请求: GET http://localhost:5888/api/ip-management/statistics
2025-08-03 12:15:49,245 [INFO] ROLLBACK
2025-08-03 12:15:49,248 [INFO] SELECT devices.manufacturer AS devices_manufacturer, count(devices.id) AS count 
FROM devices 
WHERE devices.manufacturer IS NOT NULL AND devices.manufacturer != %(manufacturer_1)s GROUP BY devices.manufacturer
2025-08-03 12:15:49,250 [INFO] ROLLBACK
2025-08-03 12:15:49,250 [INFO] ROLLBACK
2025-08-03 12:15:49,251 [INFO] [cached since 3.362s ago] {'manufacturer_1': ''}
2025-08-03 12:15:49,253 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,256 [INFO] [**********.006238] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=50 - 状态码: 200 - 耗时: 0.2506s
2025-08-03 12:15:49,258 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:49,259 [INFO] [cached since 7.377s ago] {}
2025-08-03 12:15:49,261 [INFO] [**********.008913] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.2526s
2025-08-03 12:15:49,263 [INFO] [**********.005195] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.2579s
2025-08-03 12:15:49,265 [INFO] ROLLBACK
2025-08-03 12:15:49,269 [INFO] [**********.2699] 开始请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20
2025-08-03 12:15:49,272 [INFO] ROLLBACK
2025-08-03 12:15:49,275 [INFO] [**********.039003] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=1 - 状态码: 200 - 耗时: 0.2355s
2025-08-03 12:15:49,274 [INFO] ROLLBACK
2025-08-03 12:15:49,283 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,284 [INFO] [**********.006766] 完成请求: GET http://localhost:5888/api/devices/stats - 状态码: 200 - 耗时: 0.2775s
2025-08-03 12:15:49,285 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:49,286 [INFO] [cached since 3.299s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:49,287 [INFO] [**********.246701] 完成请求: GET http://localhost:5888/api/ip-management/statistics - 状态码: 200 - 耗时: 0.0404s
2025-08-03 12:15:49,289 [INFO] [**********.289785] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:15:49,291 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,291 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:15:49,293 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,294 [INFO] [cached since 4.121s ago] {'param_1': 1}
2025-08-03 12:15:49,294 [INFO] [cached since 3.453s ago] {}
2025-08-03 12:15:49,296 [INFO] [**********.29672] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:49,296 [INFO] ROLLBACK
2025-08-03 12:15:49,297 [INFO] [**********.29672] 开始请求: GET http://localhost:5888/api/rack-management/statistics
2025-08-03 12:15:49,299 [INFO] ROLLBACK
2025-08-03 12:15:49,302 [INFO] [**********.289785] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.0126s
2025-08-03 12:15:49,309 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,317 [INFO] BEGIN (implicit)
2025-08-03 12:15:49,320 [INFO] [**********.2699] 完成请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0511s
2025-08-03 12:15:49,319 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:49,322 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:49,328 [INFO] [cached since 3.487s ago] {}
2025-08-03 12:15:49,330 [INFO] [cached since 7.448s ago] {}
2025-08-03 12:15:49,337 [INFO] ROLLBACK
2025-08-03 12:15:49,340 [INFO] [**********.29672] 完成请求: GET http://localhost:5888/api/rack-management/statistics - 状态码: 200 - 耗时: 0.0424s
2025-08-03 12:15:49,344 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:49,345 [INFO] [cached since 3.483s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:49,351 [INFO] ROLLBACK
2025-08-03 12:15:49,352 [INFO] [**********.29672] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0560s
2025-08-03 12:15:52,047 [INFO] [**********.047682] 开始请求: GET http://localhost:5888/api/ai/health
2025-08-03 12:15:52,049 [INFO] [**********.049363] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:15:52,049 [INFO] [**********.049896] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:52,296 [INFO] [**********.047682] 完成请求: GET http://localhost:5888/api/ai/health - 状态码: 200 - 耗时: 0.2493s
2025-08-03 12:15:52,298 [INFO] [**********.049363] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.2492s
2025-08-03 12:15:52,299 [INFO] [**********.049896] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.2492s
2025-08-03 12:15:52,300 [INFO] [**********.300118] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=50
2025-08-03 12:15:52,300 [INFO] [**********.300118] 开始请求: GET http://localhost:5888/api/devices/stats
2025-08-03 12:15:52,300 [INFO] [**********.300118] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=20
2025-08-03 12:15:52,301 [INFO] [**********.301112] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:52,302 [INFO] [**********.302123] 开始请求: GET http://localhost:5888/api/devices/ports/statistics
2025-08-03 12:15:52,302 [INFO] [**********.302123] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:15:52,304 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,305 [INFO] [**********.302123] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0038s
2025-08-03 12:15:52,304 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,304 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,305 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,305 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,305 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,307 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,307 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,308 [INFO] [**********.308422] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:15:52,308 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,309 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:52,309 [INFO] [cached since 6.469s ago] {}
2025-08-03 12:15:52,309 [INFO] [cached since 6.469s ago] {}
2025-08-03 12:15:52,310 [INFO] [cached since 6.469s ago] {}
2025-08-03 12:15:52,311 [INFO] [cached since 6.47s ago] {}
2025-08-03 12:15:52,311 [INFO] [cached since 10.43s ago] {}
2025-08-03 12:15:52,315 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:52,315 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:52,316 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:52,317 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:52,317 [INFO] [cached since 6.465s ago] {'device_type_1_1': 'switch', 'device_type_1_2': 'router', 'device_type_1_3': '交换机', 'device_type_1_4': '路由器'}
2025-08-03 12:15:52,317 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,318 [INFO] [cached since 6.455s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:52,318 [INFO] [cached since 6.456s ago] {'param_1': 0, 'param_2': 50}
2025-08-03 12:15:52,320 [INFO] [cached since 6.457s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:52,320 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:15:52,321 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:52,323 [INFO] [cached since 7.15s ago] {'param_1': 1}
2025-08-03 12:15:52,324 [INFO] [cached since 6.472s ago] {'device_type_1_1': 'firewall', 'device_type_1_2': 'security', 'device_type_1_3': '防火墙', 'device_type_1_4': '安全设备'}
2025-08-03 12:15:52,327 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.is_active = true) AS anon_1
2025-08-03 12:15:52,329 [INFO] ROLLBACK
2025-08-03 12:15:52,331 [INFO] [cached since 6.454s ago] {}
2025-08-03 12:15:52,331 [INFO] ROLLBACK
2025-08-03 12:15:52,333 [INFO] ROLLBACK
2025-08-03 12:15:52,334 [INFO] ROLLBACK
2025-08-03 12:15:52,334 [INFO] ROLLBACK
2025-08-03 12:15:52,336 [INFO] [**********.308422] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.0278s
2025-08-03 12:15:52,336 [INFO] SELECT devices.manufacturer AS devices_manufacturer, count(devices.id) AS count 
FROM devices 
WHERE devices.manufacturer IS NOT NULL AND devices.manufacturer != %(manufacturer_1)s GROUP BY devices.manufacturer
2025-08-03 12:15:52,337 [INFO] [cached since 6.449s ago] {'manufacturer_1': ''}
2025-08-03 12:15:52,338 [INFO] [**********.302123] 完成请求: GET http://localhost:5888/api/devices/ports/statistics - 状态码: 200 - 耗时: 0.0360s
2025-08-03 12:15:52,339 [INFO] [**********.300118] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=50 - 状态码: 200 - 耗时: 0.0391s
2025-08-03 12:15:52,340 [INFO] [**********.300118] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0407s
2025-08-03 12:15:52,341 [INFO] [**********.301112] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0408s
2025-08-03 12:15:52,343 [INFO] [**********.343491] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:52,492 [INFO] [**********.491751] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:15:52,492 [INFO] [**********.343491] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1493s
2025-08-03 12:15:52,493 [INFO] [**********.493819] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=1
2025-08-03 12:15:52,494 [INFO] [**********.494327] 开始请求: GET http://localhost:5888/api/ip-management/statistics
2025-08-03 12:15:52,494 [INFO] [**********.494327] 开始请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20
2025-08-03 12:15:52,495 [INFO] [**********.491751] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0036s
2025-08-03 12:15:52,496 [INFO] ROLLBACK
2025-08-03 12:15:52,497 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,497 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,497 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,500 [INFO] [**********.300118] 完成请求: GET http://localhost:5888/api/devices/stats - 状态码: 200 - 耗时: 0.1999s
2025-08-03 12:15:52,499 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:52,500 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:52,500 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,502 [INFO] [cached since 6.515s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:52,503 [INFO] [**********.502322] 开始请求: POST http://localhost:5888/api/license/record-login
2025-08-03 12:15:52,503 [INFO] [cached since 10.62s ago] {}
2025-08-03 12:15:52,503 [INFO] [cached since 6.663s ago] {}
2025-08-03 12:15:52,507 [INFO] [**********.502322] 完成请求: POST http://localhost:5888/api/license/record-login - 状态码: 200 - 耗时: 0.0053s
2025-08-03 12:15:52,508 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:52,512 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,516 [INFO] [**********.516162] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:52,514 [INFO] [cached since 6.652s ago] {'param_1': 0, 'param_2': 1}
2025-08-03 12:15:52,517 [INFO] [cached since 6.676s ago] {}
2025-08-03 12:15:52,518 [INFO] ROLLBACK
2025-08-03 12:15:52,520 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,520 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:52,524 [INFO] [**********.494327] 完成请求: GET http://localhost:5888/api/ip-management/statistics - 状态码: 200 - 耗时: 0.0305s
2025-08-03 12:15:52,523 [INFO] [cached since 6.683s ago] {}
2025-08-03 12:15:52,525 [INFO] ROLLBACK
2025-08-03 12:15:52,533 [INFO] [**********.494327] 完成请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0388s
2025-08-03 12:15:52,533 [INFO] ROLLBACK
2025-08-03 12:15:52,535 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:52,536 [INFO] [cached since 6.674s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:52,539 [INFO] [**********.493819] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=1 - 状态码: 200 - 耗时: 0.0458s
2025-08-03 12:15:52,546 [INFO] ROLLBACK
2025-08-03 12:15:52,547 [INFO] [**********.516162] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0318s
2025-08-03 12:15:52,573 [INFO] [**********.573514] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:15:52,574 [INFO] [**********.573514] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:15:52,583 [INFO] [**********.582912] 开始请求: GET http://localhost:5888/api/rack-management/statistics
2025-08-03 12:15:52,586 [INFO] [**********.5867] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:15:52,592 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,593 [INFO] BEGIN (implicit)
2025-08-03 12:15:52,595 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:15:52,596 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:52,597 [INFO] [cached since 7.424s ago] {'param_1': 1}
2025-08-03 12:15:52,598 [INFO] [cached since 10.72s ago] {}
2025-08-03 12:15:52,605 [INFO] [**********.605233] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:52,789 [INFO] [**********.605233] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1845s
2025-08-03 12:15:52,789 [INFO] ROLLBACK
2025-08-03 12:15:52,790 [INFO] ROLLBACK
2025-08-03 12:15:52,790 [INFO] [**********.5867] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.2041s
2025-08-03 12:15:52,791 [INFO] [**********.582912] 完成请求: GET http://localhost:5888/api/rack-management/statistics - 状态码: 200 - 耗时: 0.2089s
2025-08-03 12:15:54,321 [INFO] [**********.321967] 开始请求: POST http://localhost:5888/api/license/validate-code
2025-08-03 12:15:54,385 [INFO] [**********.321967] 完成请求: POST http://localhost:5888/api/license/validate-code - 状态码: 200 - 耗时: 0.0637s
2025-08-03 12:15:54,390 [INFO] [**********.390883] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:15:54,391 [INFO] [**********.390883] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:15:54,398 [INFO] [**********.398287] 开始请求: POST http://localhost:5888/api/license/activate
2025-08-03 12:15:54,576 [INFO] [**********.398287] 完成请求: POST http://localhost:5888/api/license/activate - 状态码: 200 - 耗时: 0.1778s
2025-08-03 12:15:54,579 [INFO] [**********.57974] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:15:54,637 [INFO] [**********.57974] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.0578s
2025-08-03 12:15:56,182 [INFO] [**********.182724] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:15:56,243 [INFO] [**********.182724] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.0609s
2025-08-03 12:15:56,247 [INFO] [**********.247791] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:15:56,249 [INFO] [**********.247791] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0021s
2025-08-03 12:15:56,338 [INFO] [**********.338006] 开始请求: GET http://localhost:5888/api/devices/stats
2025-08-03 12:15:56,338 [INFO] [**********.338006] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=50
2025-08-03 12:15:56,340 [INFO] [**********.340435] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:15:56,340 [INFO] [**********.340435] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:56,341 [INFO] [**********.341453] 开始请求: GET http://localhost:5888/api/devices/ports/statistics
2025-08-03 12:15:56,341 [INFO] [**********.341453] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=20
2025-08-03 12:15:56,344 [INFO] [**********.340435] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0043s
2025-08-03 12:15:56,344 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,347 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,348 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,349 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,349 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,350 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,350 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,351 [INFO] [cached since 10.51s ago] {}
2025-08-03 12:15:56,352 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,353 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,354 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:56,355 [INFO] [cached since 10.51s ago] {}
2025-08-03 12:15:56,356 [INFO] [**********.356922] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=1
2025-08-03 12:15:56,358 [INFO] [cached since 10.52s ago] {}
2025-08-03 12:15:56,359 [INFO] [cached since 10.52s ago] {}
2025-08-03 12:15:56,360 [INFO] [cached since 14.48s ago] {}
2025-08-03 12:15:56,365 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:56,368 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:56,371 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:56,373 [INFO] [cached since 10.52s ago] {'device_type_1_1': 'switch', 'device_type_1_2': 'router', 'device_type_1_3': '交换机', 'device_type_1_4': '路由器'}
2025-08-03 12:15:56,374 [INFO] [cached since 10.51s ago] {'param_1': 0, 'param_2': 50}
2025-08-03 12:15:56,375 [INFO] [cached since 10.51s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:56,376 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:56,378 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,380 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:15:56,385 [INFO] [cached since 10.52s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:56,387 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,388 [INFO] [cached since 10.54s ago] {'device_type_1_1': 'firewall', 'device_type_1_2': 'security', 'device_type_1_3': '防火墙', 'device_type_1_4': '安全设备'}
2025-08-03 12:15:56,389 [INFO] ROLLBACK
2025-08-03 12:15:56,392 [INFO] [cached since 10.55s ago] {}
2025-08-03 12:15:56,395 [INFO] ROLLBACK
2025-08-03 12:15:56,396 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.is_active = true) AS anon_1
2025-08-03 12:15:56,401 [INFO] [**********.341453] 完成请求: GET http://localhost:5888/api/devices/ports/statistics - 状态码: 200 - 耗时: 0.0600s
2025-08-03 12:15:56,402 [INFO] ROLLBACK
2025-08-03 12:15:56,404 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:56,405 [INFO] [cached since 10.53s ago] {}
2025-08-03 12:15:56,409 [INFO] [cached since 10.55s ago] {'param_1': 0, 'param_2': 1}
2025-08-03 12:15:56,411 [INFO] [**********.338006] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=50 - 状态码: 200 - 耗时: 0.0732s
2025-08-03 12:15:56,414 [INFO] SELECT devices.manufacturer AS devices_manufacturer, count(devices.id) AS count 
FROM devices 
WHERE devices.manufacturer IS NOT NULL AND devices.manufacturer != %(manufacturer_1)s GROUP BY devices.manufacturer
2025-08-03 12:15:56,416 [INFO] [cached since 10.53s ago] {'manufacturer_1': ''}
2025-08-03 12:15:56,421 [INFO] [**********.340435] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0815s
2025-08-03 12:15:56,425 [INFO] [**********.425127] 开始请求: GET http://localhost:5888/api/ip-management/statistics
2025-08-03 12:15:56,428 [INFO] ROLLBACK
2025-08-03 12:15:56,430 [INFO] [**********.430897] 开始请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20
2025-08-03 12:15:56,431 [INFO] ROLLBACK
2025-08-03 12:15:56,435 [INFO] [**********.341453] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0938s
2025-08-03 12:15:56,434 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,435 [INFO] ROLLBACK
2025-08-03 12:15:56,438 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:56,441 [INFO] [**********.356922] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=1 - 状态码: 200 - 耗时: 0.0843s
2025-08-03 12:15:56,440 [INFO] [cached since 14.56s ago] {}
2025-08-03 12:15:56,442 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,444 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:56,445 [INFO] [**********.338006] 完成请求: GET http://localhost:5888/api/devices/stats - 状态码: 200 - 耗时: 0.1073s
2025-08-03 12:15:56,445 [INFO] [cached since 10.46s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:15:56,452 [INFO] ROLLBACK
2025-08-03 12:15:56,456 [INFO] [**********.456119] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:15:56,454 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,458 [INFO] [cached since 10.62s ago] {}
2025-08-03 12:15:56,461 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,463 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:15:56,464 [INFO] [cached since 11.29s ago] {'param_1': 1}
2025-08-03 12:15:56,470 [INFO] [**********.470081] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:15:56,471 [INFO] ROLLBACK
2025-08-03 12:15:56,472 [INFO] [**********.425127] 完成请求: GET http://localhost:5888/api/ip-management/statistics - 状态码: 200 - 耗时: 0.0471s
2025-08-03 12:15:56,474 [INFO] ROLLBACK
2025-08-03 12:15:56,475 [INFO] [**********.456119] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.0188s
2025-08-03 12:15:56,481 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,482 [INFO] [**********.430897] 完成请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0519s
2025-08-03 12:15:56,483 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:56,487 [INFO] [**********.48706] 开始请求: GET http://localhost:5888/api/rack-management/statistics
2025-08-03 12:15:56,486 [INFO] [cached since 10.64s ago] {}
2025-08-03 12:15:56,489 [INFO] BEGIN (implicit)
2025-08-03 12:15:56,490 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:15:56,491 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:15:56,495 [INFO] [cached since 14.61s ago] {}
2025-08-03 12:15:56,496 [INFO] [cached since 10.63s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:15:56,504 [INFO] ROLLBACK
2025-08-03 12:15:56,506 [INFO] ROLLBACK
2025-08-03 12:15:56,507 [INFO] [**********.470081] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0375s
2025-08-03 12:15:56,509 [INFO] [**********.48706] 完成请求: GET http://localhost:5888/api/rack-management/statistics - 状态码: 200 - 耗时: 0.0216s
2025-08-03 12:15:58,775 [INFO] [**********.775983] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:15:58,777 [INFO] [**********.775983] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0015s
2025-08-03 12:15:58,783 [INFO] [**********.782356] 开始请求: GET http://localhost:5888/api/system/db-info
2025-08-03 12:15:58,785 [INFO] BEGIN (implicit)
2025-08-03 12:15:58,786 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:15:58,787 [INFO] [cached since 12.95s ago] {}
2025-08-03 12:15:58,791 [INFO] SELECT SUM(data_length + index_length) / 1024 / 1024 AS size_mb FROM information_schema.tables
2025-08-03 12:15:58,792 [INFO] [generated in 0.00100s] {}
2025-08-03 12:15:59,146 [INFO] ROLLBACK
2025-08-03 12:15:59,147 [INFO] [**********.782356] 完成请求: GET http://localhost:5888/api/system/db-info - 状态码: 200 - 耗时: 0.3655s
2025-08-03 12:15:59,157 [INFO] [1754194559.157094] 开始请求: GET http://localhost:5888/api/system-info
2025-08-03 12:16:00,975 [INFO] [1754194559.157094] 完成请求: GET http://localhost:5888/api/system-info - 状态码: 200 - 耗时: 1.8177s
2025-08-03 12:16:02,035 [INFO] [1754194562.035837] 开始请求: GET http://localhost:5888/api/devices/
2025-08-03 12:16:02,037 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,038 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:02,038 [INFO] [cached since 16.2s ago] {}
2025-08-03 12:16:02,044 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:02,044 [INFO] [cached since 16.18s ago] {'param_1': 0, 'param_2': 10}
2025-08-03 12:16:02,048 [INFO] ROLLBACK
2025-08-03 12:16:02,050 [INFO] [1754194562.035837] 完成请求: GET http://localhost:5888/api/devices/ - 状态码: 200 - 耗时: 0.0151s
2025-08-03 12:16:02,054 [INFO] [1754194562.054611] 开始请求: GET http://localhost:5888/api/devices/
2025-08-03 12:16:02,058 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,059 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:02,060 [INFO] [cached since 16.22s ago] {}
2025-08-03 12:16:02,064 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:02,066 [INFO] [cached since 16.2s ago] {'param_1': 0, 'param_2': 10}
2025-08-03 12:16:02,070 [INFO] ROLLBACK
2025-08-03 12:16:02,073 [INFO] [1754194562.054611] 完成请求: GET http://localhost:5888/api/devices/ - 状态码: 200 - 耗时: 0.0188s
2025-08-03 12:16:02,077 [INFO] [1754194562.077499] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:16:02,081 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,084 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:02,085 [INFO] [generated in 0.00158s] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:02,100 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:02,100 [INFO] [generated in 0.00046s] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:02,105 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:02,105 [INFO] [generated in 0.00052s] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:02,110 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:02,111 [INFO] [cached since 0.01161s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:02,112 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:02,113 [INFO] [cached since 0.007954s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:02,116 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:02,117 [INFO] [cached since 0.01755s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:02,119 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:02,120 [INFO] [cached since 0.0151s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:02,124 [INFO] ROLLBACK
2025-08-03 12:16:02,128 [INFO] [1754194562.077499] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0510s
2025-08-03 12:16:02,131 [INFO] [1754194562.131504] 开始请求: GET http://localhost:5888/api/rack-management/
2025-08-03 12:16:02,135 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,135 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:02,136 [INFO] [generated in 0.00027s] {}
2025-08-03 12:16:02,140 [INFO] 
            SELECT id, name, location, total_u, used_u, width, depth, 
                  temperature, humidity, power, max_power, status, description,
                  created_at, updated_at
            FROM racks
            LIMIT %(limit)s OFFSET %(skip)s
        
2025-08-03 12:16:02,141 [INFO] [generated in 0.00122s] {'limit': 100, 'skip': 0}
2025-08-03 12:16:02,142 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:02,143 [INFO] [generated in 0.00025s] {'rack_id': 'A01'}
2025-08-03 12:16:02,144 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:02,145 [INFO] [cached since 0.003673s ago] {'rack_id': 'A02'}
2025-08-03 12:16:02,148 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:02,148 [INFO] [cached since 0.0057s ago] {'rack_id': 'B01'}
2025-08-03 12:16:02,150 [INFO] ROLLBACK
2025-08-03 12:16:02,152 [INFO] [1754194562.131504] 完成请求: GET http://localhost:5888/api/rack-management/ - 状态码: 200 - 耗时: 0.0209s
2025-08-03 12:16:02,154 [INFO] [1754194562.154768] 开始请求: GET http://localhost:5888/api/integration/devices-with-ips?page=1&page_size=100
2025-08-03 12:16:02,160 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,161 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:02,162 [INFO] [cached since 16.18s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:02,171 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,172 [INFO] [generated in 0.00091s] {'ip_address_1': '***********', 'param_1': 1}
2025-08-03 12:16:02,183 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,184 [INFO] [cached since 0.01295s ago] {'ip_address_1': '***********', 'param_1': 1}
2025-08-03 12:16:02,186 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,187 [INFO] [cached since 0.01684s ago] {'ip_address_1': '************0', 'param_1': 1}
2025-08-03 12:16:02,190 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,190 [INFO] [cached since 0.01962s ago] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:02,192 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,192 [INFO] [cached since 0.02234s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:02,196 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,196 [INFO] [cached since 0.02561s ago] {'ip_address_1': '***********1', 'param_1': 1}
2025-08-03 12:16:02,200 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,201 [INFO] [cached since 0.03025s ago] {'ip_address_1': '************02', 'param_1': 1}
2025-08-03 12:16:02,206 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,211 [INFO] [cached since 0.03938s ago] {'ip_address_1': '************03', 'param_1': 1}
2025-08-03 12:16:02,213 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,214 [INFO] [cached since 0.04303s ago] {'ip_address_1': '***********2', 'param_1': 1}
2025-08-03 12:16:02,215 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,216 [INFO] [cached since 0.04605s ago] {'ip_address_1': '***********9', 'param_1': 1}
2025-08-03 12:16:02,219 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,220 [INFO] [cached since 0.05003s ago] {'ip_address_1': '***********4', 'param_1': 1}
2025-08-03 12:16:02,226 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,229 [INFO] [cached since 0.05811s ago] {'ip_address_1': '***********0', 'param_1': 1}
2025-08-03 12:16:02,238 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,239 [INFO] [cached since 0.06849s ago] {'ip_address_1': '***********2', 'param_1': 1}
2025-08-03 12:16:02,244 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,246 [INFO] [cached since 0.07604s ago] {'ip_address_1': '***********1', 'param_1': 1}
2025-08-03 12:16:02,251 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,252 [INFO] [cached since 0.08062s ago] {'ip_address_1': '***********7', 'param_1': 1}
2025-08-03 12:16:02,256 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,261 [INFO] [cached since 0.09023s ago] {'ip_address_1': '***********8', 'param_1': 1}
2025-08-03 12:16:02,264 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,270 [INFO] [cached since 0.09846s ago] {'ip_address_1': '***********6', 'param_1': 1}
2025-08-03 12:16:02,276 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,277 [INFO] [cached since 0.1066s ago] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:02,280 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,281 [INFO] [cached since 0.1103s ago] {'ip_address_1': '************0', 'param_1': 1}
2025-08-03 12:16:02,286 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,287 [INFO] [cached since 0.1166s ago] {'ip_address_1': '************06', 'param_1': 1}
2025-08-03 12:16:02,292 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,293 [INFO] [cached since 0.1221s ago] {'ip_address_1': '*********', 'param_1': 1}
2025-08-03 12:16:02,296 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,299 [INFO] [cached since 0.1286s ago] {'ip_address_1': '*********1', 'param_1': 1}
2025-08-03 12:16:02,303 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,303 [INFO] [cached since 0.1326s ago] {'ip_address_1': '*********', 'param_1': 1}
2025-08-03 12:16:02,305 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,306 [INFO] [cached since 0.1352s ago] {'ip_address_1': '**********', 'param_1': 1}
2025-08-03 12:16:02,310 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,311 [INFO] [cached since 0.1406s ago] {'ip_address_1': '************01', 'param_1': 1}
2025-08-03 12:16:02,315 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,316 [INFO] [cached since 0.1455s ago] {'ip_address_1': '*********', 'param_1': 1}
2025-08-03 12:16:02,320 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,321 [INFO] [cached since 0.1508s ago] {'ip_address_1': '*********0', 'param_1': 1}
2025-08-03 12:16:02,326 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,327 [INFO] [cached since 0.1569s ago] {'ip_address_1': '************05', 'param_1': 1}
2025-08-03 12:16:02,332 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,334 [INFO] [cached since 0.1635s ago] {'ip_address_1': '************04', 'param_1': 1}
2025-08-03 12:16:02,336 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,337 [INFO] [cached since 0.1666s ago] {'ip_address_1': '************07', 'param_1': 1}
2025-08-03 12:16:02,340 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,341 [INFO] [cached since 0.1698s ago] {'ip_address_1': '************08', 'param_1': 1}
2025-08-03 12:16:02,342 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,343 [INFO] [cached since 0.1723s ago] {'ip_address_1': '*********', 'param_1': 1}
2025-08-03 12:16:02,346 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,347 [INFO] [cached since 0.1763s ago] {'ip_address_1': '************12', 'param_1': 1}
2025-08-03 12:16:02,350 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,351 [INFO] [cached since 0.1798s ago] {'ip_address_1': '************09', 'param_1': 1}
2025-08-03 12:16:02,352 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,353 [INFO] [cached since 0.1821s ago] {'ip_address_1': '************10', 'param_1': 1}
2025-08-03 12:16:02,354 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,354 [INFO] [cached since 0.1835s ago] {'ip_address_1': '************11', 'param_1': 1}
2025-08-03 12:16:02,357 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,357 [INFO] [cached since 0.1863s ago] {'ip_address_1': '************1', 'param_1': 1}
2025-08-03 12:16:02,359 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:02,359 [INFO] [cached since 0.1886s ago] {'ip_address_1': '**********', 'param_1': 1}
2025-08-03 12:16:02,361 [INFO] ROLLBACK
2025-08-03 12:16:02,362 [INFO] [1754194562.154768] 完成请求: GET http://localhost:5888/api/integration/devices-with-ips?page=1&page_size=100 - 状态码: 200 - 耗时: 0.2074s
2025-08-03 12:16:02,364 [INFO] [1754194562.364803] 开始请求: GET http://localhost:5888/api/devices/
2025-08-03 12:16:02,365 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,365 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:02,366 [INFO] [cached since 16.53s ago] {}
2025-08-03 12:16:02,368 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:02,369 [INFO] [cached since 16.51s ago] {'param_1': 0, 'param_2': 10}
2025-08-03 12:16:02,370 [INFO] [1754194562.370868] 开始请求: GET http://localhost:5888/api/direct-devices?_t=1754194562192
2025-08-03 12:16:02,372 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,372 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:16:02,373 [INFO] [cached since 20.49s ago] {}
2025-08-03 12:16:02,375 [INFO] ROLLBACK
2025-08-03 12:16:02,376 [INFO] [1754194562.364803] 完成请求: GET http://localhost:5888/api/devices/ - 状态码: 200 - 耗时: 0.0117s
2025-08-03 12:16:02,381 [INFO] ROLLBACK
2025-08-03 12:16:02,382 [INFO] [1754194562.370868] 完成请求: GET http://localhost:5888/api/direct-devices?_t=1754194562192 - 状态码: 200 - 耗时: 0.0118s
2025-08-03 12:16:02,552 [INFO] [1754194562.552285] 开始请求: GET http://localhost:5888/api/direct-devices?_t=1754194562514
2025-08-03 12:16:02,553 [INFO] BEGIN (implicit)
2025-08-03 12:16:02,554 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:16:02,554 [INFO] [cached since 20.67s ago] {}
2025-08-03 12:16:02,558 [INFO] ROLLBACK
2025-08-03 12:16:02,559 [INFO] [1754194562.552285] 完成请求: GET http://localhost:5888/api/direct-devices?_t=1754194562514 - 状态码: 200 - 耗时: 0.0072s
2025-08-03 12:16:03,791 [INFO] [1754194563.791465] 开始请求: GET http://localhost:5888/api/config/deployment-logs
2025-08-03 12:16:03,792 [INFO] [1754194563.792872] 开始请求: GET http://localhost:5888/api/config/configs
2025-08-03 12:16:03,793 [INFO] BEGIN (implicit)
2025-08-03 12:16:03,794 [INFO] [1754194563.791465] 完成请求: GET http://localhost:5888/api/config/deployment-logs - 状态码: 200 - 耗时: 0.0034s
2025-08-03 12:16:03,795 [INFO] SELECT configurations.id AS configurations_id, configurations.device_id AS configurations_device_id, configurations.config_name AS configurations_config_name, configurations.config_content AS configurations_config_content, configurations.config_type AS configurations_config_type, configurations.vendor AS configurations_vendor, configurations.is_active AS configurations_is_active, configurations.is_deployed AS configurations_is_deployed, configurations.created_at AS configurations_created_at, configurations.updated_at AS configurations_updated_at 
FROM configurations 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:03,795 [INFO] [generated in 0.00057s] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:03,798 [INFO] 成功查询到 0 个配置
2025-08-03 12:16:03,798 [INFO] 成功处理并返回 0 个配置项
2025-08-03 12:16:03,800 [INFO] ROLLBACK
2025-08-03 12:16:03,801 [INFO] [1754194563.792872] 完成请求: GET http://localhost:5888/api/config/configs - 状态码: 200 - 耗时: 0.0086s
2025-08-03 12:16:03,838 [INFO] [1754194563.838942] 开始请求: GET http://localhost:5888/api/config/devices?skip=0&limit=10
2025-08-03 12:16:03,841 [INFO] BEGIN (implicit)
2025-08-03 12:16:03,843 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:03,843 [INFO] [cached since 18s ago] {}
2025-08-03 12:16:03,847 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:03,849 [INFO] [cached since 17.99s ago] {'param_1': 0, 'param_2': 10}
2025-08-03 12:16:03,853 [INFO] ROLLBACK
2025-08-03 12:16:03,855 [INFO] [1754194563.838942] 完成请求: GET http://localhost:5888/api/config/devices?skip=0&limit=10 - 状态码: 200 - 耗时: 0.0157s
2025-08-03 12:16:03,891 [INFO] [1754194563.891717] 开始请求: GET http://localhost:5888/api/config/deployment-logs
2025-08-03 12:16:03,893 [INFO] [1754194563.891717] 完成请求: GET http://localhost:5888/api/config/deployment-logs - 状态码: 200 - 耗时: 0.0016s
2025-08-03 12:16:04,785 [INFO] [1754194564.785052] 开始请求: GET http://localhost:5888/api/inspection/inspection_devices?skip=0&limit=10
2025-08-03 12:16:04,786 [INFO] 请求巡检设备列表，skip=0, limit=10
2025-08-03 12:16:04,788 [INFO] 数据库中共有 38 台设备
2025-08-03 12:16:04,788 [INFO] 成功获取 10 台巡检设备
2025-08-03 12:16:04,789 [INFO] [1754194564.785052] 完成请求: GET http://localhost:5888/api/inspection/inspection_devices?skip=0&limit=10 - 状态码: 200 - 耗时: 0.0041s
2025-08-03 12:16:04,834 [INFO] [1754194564.834172] 开始请求: GET http://localhost:5888/api/inspection/result/1
2025-08-03 12:16:04,836 [INFO] 请求设备ID 1 的巡检结果，file_path=None
2025-08-03 12:16:04,840 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,841 [INFO] 设备 1 的所有巡检结果文件: 0 个
2025-08-03 12:16:04,842 [INFO] 设备ID 1 没有巡检结果文件
2025-08-03 12:16:04,844 [INFO] [1754194564.834172] 完成请求: GET http://localhost:5888/api/inspection/result/1 - 状态码: 200 - 耗时: 0.0107s
2025-08-03 12:16:04,859 [INFO] [1754194564.859533] 开始请求: GET http://localhost:5888/api/inspection/result/2
2025-08-03 12:16:04,861 [INFO] 请求设备ID 2 的巡检结果，file_path=None
2025-08-03 12:16:04,864 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,865 [INFO] 设备 2 的所有巡检结果文件: 0 个
2025-08-03 12:16:04,866 [INFO] 设备ID 2 没有巡检结果文件
2025-08-03 12:16:04,867 [INFO] [1754194564.859533] 完成请求: GET http://localhost:5888/api/inspection/result/2 - 状态码: 200 - 耗时: 0.0078s
2025-08-03 12:16:04,871 [INFO] [1754194564.871636] 开始请求: GET http://localhost:5888/api/inspection/result/4
2025-08-03 12:16:04,873 [INFO] 请求设备ID 4 的巡检结果，file_path=None
2025-08-03 12:16:04,875 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,877 [INFO] 设备 4 的所有巡检结果文件: 0 个
2025-08-03 12:16:04,877 [INFO] 设备ID 4 没有巡检结果文件
2025-08-03 12:16:04,880 [INFO] [1754194564.871636] 完成请求: GET http://localhost:5888/api/inspection/result/4 - 状态码: 200 - 耗时: 0.0084s
2025-08-03 12:16:04,897 [INFO] [1754194564.897014] 开始请求: GET http://localhost:5888/api/inspection/result/5
2025-08-03 12:16:04,898 [INFO] 请求设备ID 5 的巡检结果，file_path=None
2025-08-03 12:16:04,904 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,906 [INFO] 设备 5 的所有巡检结果文件: 0 个
2025-08-03 12:16:04,907 [INFO] 设备ID 5 没有巡检结果文件
2025-08-03 12:16:04,909 [INFO] [1754194564.897014] 完成请求: GET http://localhost:5888/api/inspection/result/5 - 状态码: 200 - 耗时: 0.0129s
2025-08-03 12:16:04,924 [INFO] [1754194564.923864] 开始请求: GET http://localhost:5888/api/inspection/result/6
2025-08-03 12:16:04,926 [INFO] 请求设备ID 6 的巡检结果，file_path=None
2025-08-03 12:16:04,932 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,934 [INFO] 设备 6 的所有巡检结果文件: 0 个
2025-08-03 12:16:04,934 [INFO] 设备ID 6 没有巡检结果文件
2025-08-03 12:16:04,937 [INFO] [1754194564.923864] 完成请求: GET http://localhost:5888/api/inspection/result/6 - 状态码: 200 - 耗时: 0.0132s
2025-08-03 12:16:04,950 [INFO] [1754194564.949778] 开始请求: GET http://localhost:5888/api/inspection/result/7
2025-08-03 12:16:04,952 [INFO] 请求设备ID 7 的巡检结果，file_path=None
2025-08-03 12:16:04,957 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,959 [INFO] 设备 7 的所有巡检结果文件: 0 个
2025-08-03 12:16:04,959 [INFO] 设备ID 7 没有巡检结果文件
2025-08-03 12:16:04,962 [INFO] [1754194564.949778] 完成请求: GET http://localhost:5888/api/inspection/result/7 - 状态码: 200 - 耗时: 0.0126s
2025-08-03 12:16:04,981 [INFO] [1754194564.981244] 开始请求: GET http://localhost:5888/api/inspection/result/8
2025-08-03 12:16:04,983 [INFO] 请求设备ID 8 的巡检结果，file_path=None
2025-08-03 12:16:04,988 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:04,989 [INFO] 在新目录结构 inspection_results\20250723\huawei\SW02_************02 中找到 1 个文件
2025-08-03 12:16:04,990 [INFO] 在新目录结构 inspection_results\20250722\huawei\SW02_************02 中找到 1 个文件
2025-08-03 12:16:04,991 [INFO] 在新目录结构 inspection_results\20250721\huawei\SW02_************02 中找到 1 个文件
2025-08-03 12:16:04,992 [INFO] 在新目录结构 inspection_results\20250719\huawei\SW02_************02 中找到 4 个文件
2025-08-03 12:16:04,994 [INFO] 在新目录结构 inspection_results\20250715\huawei\SW02_************02 中找到 1 个文件
2025-08-03 12:16:04,995 [INFO] 设备 8 的所有巡检结果文件: 8 个
2025-08-03 12:16:04,997 [INFO] [1754194564.981244] 完成请求: GET http://localhost:5888/api/inspection/result/8 - 状态码: 200 - 耗时: 0.0159s
2025-08-03 12:16:05,022 [INFO] [1754194565.022997] 开始请求: GET http://localhost:5888/api/inspection/result/9
2025-08-03 12:16:05,024 [INFO] 请求设备ID 9 的巡检结果，file_path=None
2025-08-03 12:16:05,026 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:05,027 [INFO] 在新目录结构 inspection_results\20250723\huawei\SW03_************03 中找到 1 个文件
2025-08-03 12:16:05,027 [INFO] 在新目录结构 inspection_results\20250722\huawei\SW03_************03 中找到 1 个文件
2025-08-03 12:16:05,028 [INFO] 在新目录结构 inspection_results\20250721\huawei\SW03_************03 中找到 1 个文件
2025-08-03 12:16:05,029 [INFO] 在新目录结构 inspection_results\20250719\huawei\SW03_************03 中找到 4 个文件
2025-08-03 12:16:05,029 [INFO] 在新目录结构 inspection_results\20250715\huawei\SW03_************03 中找到 1 个文件
2025-08-03 12:16:05,030 [INFO] 在新目录结构 inspection_results\20250706\huawei\SW03_************03 中找到 2 个文件
2025-08-03 12:16:05,031 [INFO] 在新目录结构 inspection_results\20250702\huawei\SW03_************03 中找到 1 个文件
2025-08-03 12:16:05,031 [INFO] 设备 9 的所有巡检结果文件: 11 个
2025-08-03 12:16:05,032 [INFO] [1754194565.022997] 完成请求: GET http://localhost:5888/api/inspection/result/9 - 状态码: 200 - 耗时: 0.0094s
2025-08-03 12:16:05,057 [INFO] [1754194565.057158] 开始请求: GET http://localhost:5888/api/inspection/result/10
2025-08-03 12:16:05,058 [INFO] 请求设备ID 10 的巡检结果，file_path=None
2025-08-03 12:16:05,064 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:05,064 [INFO] 设备 10 的所有巡检结果文件: 0 个
2025-08-03 12:16:05,065 [INFO] 设备ID 10 没有巡检结果文件
2025-08-03 12:16:05,065 [INFO] [1754194565.057158] 完成请求: GET http://localhost:5888/api/inspection/result/10 - 状态码: 200 - 耗时: 0.0085s
2025-08-03 12:16:05,069 [INFO] [1754194565.069263] 开始请求: GET http://localhost:5888/api/inspection/result/12
2025-08-03 12:16:05,070 [INFO] 请求设备ID 12 的巡检结果，file_path=None
2025-08-03 12:16:05,078 [INFO] 找到日期目录: ['20250723', '20250722', '20250721', '20250719', '20250715', '20250706', '20250702', '20250701', '20250629']
2025-08-03 12:16:05,079 [INFO] 设备 12 的所有巡检结果文件: 0 个
2025-08-03 12:16:05,080 [INFO] 设备ID 12 没有巡检结果文件
2025-08-03 12:16:05,083 [INFO] [1754194565.069263] 完成请求: GET http://localhost:5888/api/inspection/result/12 - 状态码: 200 - 耗时: 0.0146s
2025-08-03 12:16:05,985 [INFO] [1754194565.985699] 开始请求: GET http://localhost:5888/api/config/debug/test
2025-08-03 12:16:05,986 [INFO] [1754194565.985699] 完成请求: GET http://localhost:5888/api/config/debug/test - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:16:05,999 [INFO] [1754194565.999802] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=10
2025-08-03 12:16:06,002 [INFO] BEGIN (implicit)
2025-08-03 12:16:06,002 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:06,004 [INFO] [cached since 20.16s ago] {}
2025-08-03 12:16:06,007 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:06,008 [INFO] [cached since 20.15s ago] {'param_1': 0, 'param_2': 10}
2025-08-03 12:16:06,010 [INFO] ROLLBACK
2025-08-03 12:16:06,011 [INFO] [1754194565.999802] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=10 - 状态码: 200 - 耗时: 0.0117s
2025-08-03 12:16:08,195 [INFO] [1754194568.195691] 开始请求: GET http://localhost:5888/api/ip-management/ip-addresses/?page=1&page_size=20
2025-08-03 12:16:08,196 [INFO] [1754194568.196224] 开始请求: GET http://localhost:5888/api/ip-management/statistics/
2025-08-03 12:16:08,196 [INFO] [1754194568.196735] 开始请求: GET http://localhost:5888/api/ip-management/subnets/
2025-08-03 12:16:08,198 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,198 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,199 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,199 [INFO] SELECT count(ip_addresses.id) AS count_1 
FROM ip_addresses
2025-08-03 12:16:08,200 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses
2025-08-03 12:16:08,201 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets
2025-08-03 12:16:08,201 [INFO] [generated in 0.00207s] {}
2025-08-03 12:16:08,201 [INFO] [generated in 0.00148s] {}
2025-08-03 12:16:08,202 [INFO] [generated in 0.00081s] {}
2025-08-03 12:16:08,205 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses ORDER BY ip_addresses.ip_address ASC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:08,207 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets
2025-08-03 12:16:08,207 [INFO] [generated in 0.00179s] {'param_1': 0, 'param_2': 20}
2025-08-03 12:16:08,208 [INFO] [cached since 0.006803s ago] {}
2025-08-03 12:16:08,209 [INFO] ROLLBACK
2025-08-03 12:16:08,211 [INFO] [1754194568.196735] 完成请求: GET http://localhost:5888/api/ip-management/subnets/ - 状态码: 200 - 耗时: 0.0150s
2025-08-03 12:16:08,214 [INFO] ROLLBACK
2025-08-03 12:16:08,214 [INFO] ROLLBACK
2025-08-03 12:16:08,216 [INFO] [1754194568.195691] 完成请求: GET http://localhost:5888/api/ip-management/ip-addresses/?page=1&page_size=20 - 状态码: 200 - 耗时: 0.0209s
2025-08-03 12:16:08,217 [INFO] [1754194568.196224] 完成请求: GET http://localhost:5888/api/ip-management/statistics/ - 状态码: 200 - 耗时: 0.0213s
2025-08-03 12:16:08,240 [INFO] [1754194568.240414] 开始请求: GET http://localhost:5888/api/ip-management/subnets/2/usage
2025-08-03 12:16:08,241 [INFO] [1754194568.241966] 开始请求: GET http://localhost:5888/api/ip-management/subnets/1/usage
2025-08-03 12:16:08,242 [INFO] [1754194568.242493] 开始请求: GET http://localhost:5888/api/ip-management/subnets/3/usage
2025-08-03 12:16:08,248 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,251 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,251 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,255 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,256 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,257 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,258 [INFO] [generated in 0.00313s] {'id_1': 2, 'param_1': 1}
2025-08-03 12:16:08,258 [INFO] [cached since 0.003482s ago] {'id_1': 3, 'param_1': 1}
2025-08-03 12:16:08,258 [INFO] [cached since 0.003778s ago] {'id_1': 1, 'param_1': 1}
2025-08-03 12:16:08,264 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,266 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,267 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,267 [INFO] [generated in 0.00349s] {'subnet_id_1': 2}
2025-08-03 12:16:08,268 [INFO] [cached since 0.003923s ago] {'subnet_id_1': 1}
2025-08-03 12:16:08,268 [INFO] [cached since 0.004349s ago] {'subnet_id_1': 3}
2025-08-03 12:16:08,275 [INFO] ROLLBACK
2025-08-03 12:16:08,276 [INFO] ROLLBACK
2025-08-03 12:16:08,279 [INFO] [1754194568.241966] 完成请求: GET http://localhost:5888/api/ip-management/subnets/1/usage - 状态码: 200 - 耗时: 0.0375s
2025-08-03 12:16:08,284 [INFO] [1754194568.242493] 完成请求: GET http://localhost:5888/api/ip-management/subnets/3/usage - 状态码: 200 - 耗时: 0.0419s
2025-08-03 12:16:08,287 [INFO] ROLLBACK
2025-08-03 12:16:08,290 [INFO] [1754194568.240414] 完成请求: GET http://localhost:5888/api/ip-management/subnets/2/usage - 状态码: 200 - 耗时: 0.0499s
2025-08-03 12:16:08,402 [INFO] [1754194568.402969] 开始请求: GET http://localhost:5888/api/integration/ips-with-devices?page=1&page_size=20&status=
2025-08-03 12:16:08,407 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,408 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:08,408 [INFO] [generated in 0.00066s] {'param_1': 0, 'param_2': 20}
2025-08-03 12:16:08,413 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,413 [INFO] [generated in 0.00113s] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:08,420 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,421 [INFO] [cached since 0.007713s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,424 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,426 [INFO] [cached since 0.01353s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,431 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,433 [INFO] [cached since 0.02002s ago] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:08,436 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,437 [INFO] [cached since 0.02419s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,440 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,442 [INFO] [cached since 0.02849s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,444 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,444 [INFO] [cached since 0.03146s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,447 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,448 [INFO] [cached since 0.03504s ago] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:08,449 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,450 [INFO] [cached since 0.03734s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,452 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,454 [INFO] [cached since 0.041s ago] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:08,456 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,458 [INFO] [cached since 0.04489s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,464 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,466 [INFO] [cached since 0.05325s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,474 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,476 [INFO] [cached since 0.06306s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,480 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,484 [INFO] [cached since 0.0706s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,491 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,493 [INFO] [cached since 0.08026s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,498 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,501 [INFO] [cached since 0.0878s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,508 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,508 [INFO] [cached since 0.09654s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,513 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,516 [INFO] [cached since 0.1031s ago] {'ip_address_1': '************1', 'param_1': 1}
2025-08-03 12:16:08,523 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,544 [INFO] [cached since 0.1305s ago] {'ip_address_1': '*************', 'param_1': 1}
2025-08-03 12:16:08,551 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.ip_address = %(ip_address_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,552 [INFO] [cached since 0.1395s ago] {'ip_address_1': '************', 'param_1': 1}
2025-08-03 12:16:08,561 [INFO] [1754194568.56192] 开始请求: GET http://localhost:5888/api/ip-management/subnets/2/usage
2025-08-03 12:16:08,562 [INFO] ROLLBACK
2025-08-03 12:16:08,565 [INFO] [1754194568.565842] 开始请求: GET http://localhost:5888/api/ip-management/subnets/1/usage
2025-08-03 12:16:08,568 [INFO] [1754194568.568592] 开始请求: GET http://localhost:5888/api/ip-management/subnets/3/usage
2025-08-03 12:16:08,572 [INFO] [1754194568.402969] 完成请求: GET http://localhost:5888/api/integration/ips-with-devices?page=1&page_size=20&status= - 状态码: 200 - 耗时: 0.1700s
2025-08-03 12:16:08,579 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,580 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,581 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,582 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,583 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,585 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,585 [INFO] [cached since 0.3303s ago] {'id_1': 2, 'param_1': 1}
2025-08-03 12:16:08,587 [INFO] [cached since 0.3317s ago] {'id_1': 3, 'param_1': 1}
2025-08-03 12:16:08,587 [INFO] [cached since 0.3324s ago] {'id_1': 1, 'param_1': 1}
2025-08-03 12:16:08,592 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,594 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,595 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,597 [INFO] [cached since 0.3329s ago] {'subnet_id_1': 1}
2025-08-03 12:16:08,598 [INFO] [cached since 0.3335s ago] {'subnet_id_1': 2}
2025-08-03 12:16:08,598 [INFO] [cached since 0.334s ago] {'subnet_id_1': 3}
2025-08-03 12:16:08,605 [INFO] ROLLBACK
2025-08-03 12:16:08,607 [INFO] ROLLBACK
2025-08-03 12:16:08,610 [INFO] [1754194568.565842] 完成请求: GET http://localhost:5888/api/ip-management/subnets/1/usage - 状态码: 200 - 耗时: 0.0449s
2025-08-03 12:16:08,616 [INFO] [1754194568.568592] 完成请求: GET http://localhost:5888/api/ip-management/subnets/3/usage - 状态码: 200 - 耗时: 0.0477s
2025-08-03 12:16:08,622 [INFO] ROLLBACK
2025-08-03 12:16:08,625 [INFO] [1754194568.56192] 完成请求: GET http://localhost:5888/api/ip-management/subnets/2/usage - 状态码: 200 - 耗时: 0.0629s
2025-08-03 12:16:08,720 [INFO] [1754194568.720293] 开始请求: GET http://localhost:5888/api/ip-management/subnets/1/usage
2025-08-03 12:16:08,721 [INFO] [1754194568.721358] 开始请求: GET http://localhost:5888/api/ip-management/subnets/3/usage
2025-08-03 12:16:08,721 [INFO] [1754194568.721358] 开始请求: GET http://localhost:5888/api/ip-management/subnets/2/usage
2025-08-03 12:16:08,723 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,723 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,723 [INFO] BEGIN (implicit)
2025-08-03 12:16:08,724 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,725 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,725 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:08,725 [INFO] [cached since 0.4711s ago] {'id_1': 2, 'param_1': 1}
2025-08-03 12:16:08,726 [INFO] [cached since 0.4714s ago] {'id_1': 3, 'param_1': 1}
2025-08-03 12:16:08,726 [INFO] [cached since 0.4717s ago] {'id_1': 1, 'param_1': 1}
2025-08-03 12:16:08,729 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,731 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,732 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:08,732 [INFO] [cached since 0.468s ago] {'subnet_id_1': 3}
2025-08-03 12:16:08,733 [INFO] [cached since 0.4686s ago] {'subnet_id_1': 1}
2025-08-03 12:16:08,733 [INFO] [cached since 0.4689s ago] {'subnet_id_1': 2}
2025-08-03 12:16:08,736 [INFO] ROLLBACK
2025-08-03 12:16:08,738 [INFO] ROLLBACK
2025-08-03 12:16:08,740 [INFO] [1754194568.720293] 完成请求: GET http://localhost:5888/api/ip-management/subnets/1/usage - 状态码: 200 - 耗时: 0.0205s
2025-08-03 12:16:08,742 [INFO] [1754194568.721358] 完成请求: GET http://localhost:5888/api/ip-management/subnets/3/usage - 状态码: 200 - 耗时: 0.0215s
2025-08-03 12:16:08,746 [INFO] ROLLBACK
2025-08-03 12:16:08,747 [INFO] [1754194568.721358] 完成请求: GET http://localhost:5888/api/ip-management/subnets/2/usage - 状态码: 200 - 耗时: 0.0262s
2025-08-03 12:16:09,701 [INFO] [**********.70161] 开始请求: GET http://localhost:5888/api/ip-management/statistics/
2025-08-03 12:16:09,703 [INFO] BEGIN (implicit)
2025-08-03 12:16:09,703 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses
2025-08-03 12:16:09,703 [INFO] [cached since 1.504s ago] {}
2025-08-03 12:16:09,707 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets
2025-08-03 12:16:09,707 [INFO] [cached since 1.506s ago] {}
2025-08-03 12:16:09,710 [INFO] ROLLBACK
2025-08-03 12:16:09,712 [INFO] [**********.70161] 完成请求: GET http://localhost:5888/api/ip-management/statistics/ - 状态码: 200 - 耗时: 0.0110s
2025-08-03 12:16:09,721 [INFO] [**********.721839] 开始请求: GET http://localhost:5888/api/ip-management/subnets/1/usage
2025-08-03 12:16:09,723 [INFO] [**********.722968] 开始请求: GET http://localhost:5888/api/ip-management/subnets/3/usage
2025-08-03 12:16:09,723 [INFO] [**********.72347] 开始请求: GET http://localhost:5888/api/ip-management/subnets/2/usage
2025-08-03 12:16:09,726 [INFO] BEGIN (implicit)
2025-08-03 12:16:09,727 [INFO] BEGIN (implicit)
2025-08-03 12:16:09,727 [INFO] BEGIN (implicit)
2025-08-03 12:16:09,728 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:09,729 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:09,729 [INFO] SELECT subnets.id AS subnets_id, subnets.name AS subnets_name, subnets.cidr AS subnets_cidr, subnets.vlan AS subnets_vlan, subnets.gateway AS subnets_gateway, subnets.description AS subnets_description, subnets.created_at AS subnets_created_at, subnets.updated_at AS subnets_updated_at 
FROM subnets 
WHERE subnets.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:09,730 [INFO] [cached since 1.475s ago] {'id_1': 1, 'param_1': 1}
2025-08-03 12:16:09,730 [INFO] [cached since 1.475s ago] {'id_1': 3, 'param_1': 1}
2025-08-03 12:16:09,730 [INFO] [cached since 1.476s ago] {'id_1': 2, 'param_1': 1}
2025-08-03 12:16:09,732 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:09,733 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:09,733 [INFO] SELECT ip_addresses.id AS ip_addresses_id, ip_addresses.ip_address AS ip_addresses_ip_address, ip_addresses.subnet_id AS ip_addresses_subnet_id, ip_addresses.mac_address AS ip_addresses_mac_address, ip_addresses.status AS ip_addresses_status, ip_addresses.hostname AS ip_addresses_hostname, ip_addresses.purpose AS ip_addresses_purpose, ip_addresses.last_seen AS ip_addresses_last_seen, ip_addresses.created_at AS ip_addresses_created_at, ip_addresses.updated_at AS ip_addresses_updated_at, ip_addresses.device_type AS ip_addresses_device_type, ip_addresses.manufacturer AS ip_addresses_manufacturer, ip_addresses.device_model AS ip_addresses_device_model, ip_addresses.os_info AS ip_addresses_os_info, ip_addresses.open_ports AS ip_addresses_open_ports, ip_addresses.service_info AS ip_addresses_service_info, ip_addresses.hostname_source AS ip_addresses_hostname_source 
FROM ip_addresses 
WHERE ip_addresses.subnet_id = %(subnet_id_1)s
2025-08-03 12:16:09,733 [INFO] [cached since 1.469s ago] {'subnet_id_1': 1}
2025-08-03 12:16:09,734 [INFO] [cached since 1.47s ago] {'subnet_id_1': 2}
2025-08-03 12:16:09,734 [INFO] [cached since 1.47s ago] {'subnet_id_1': 3}
2025-08-03 12:16:09,738 [INFO] ROLLBACK
2025-08-03 12:16:09,740 [INFO] ROLLBACK
2025-08-03 12:16:09,741 [INFO] [**********.721839] 完成请求: GET http://localhost:5888/api/ip-management/subnets/1/usage - 状态码: 200 - 耗时: 0.0196s
2025-08-03 12:16:09,744 [INFO] [**********.722968] 完成请求: GET http://localhost:5888/api/ip-management/subnets/3/usage - 状态码: 200 - 耗时: 0.0206s
2025-08-03 12:16:09,747 [INFO] ROLLBACK
2025-08-03 12:16:09,748 [INFO] [**********.72347] 完成请求: GET http://localhost:5888/api/ip-management/subnets/2/usage - 状态码: 200 - 耗时: 0.0252s
2025-08-03 12:16:14,454 [INFO] [**********.454862] 开始请求: GET http://localhost:5888/api/ai-operations/health-metrics
2025-08-03 12:16:14,456 [INFO] BEGIN (implicit)
2025-08-03 12:16:14,457 [INFO] SELECT count(devices.id) AS count_1 
FROM devices
2025-08-03 12:16:14,457 [INFO] [generated in 0.00026s] {}
2025-08-03 12:16:14,459 [INFO] SELECT count(devices.id) AS count_1 
FROM devices 
WHERE devices.is_active = true
2025-08-03 12:16:14,459 [INFO] [generated in 0.00034s] {}
2025-08-03 12:16:14,462 [INFO] SELECT inspection_results.id AS inspection_results_id, inspection_results.device_id AS inspection_results_device_id, inspection_results.inspection_time AS inspection_results_inspection_time, inspection_results.file_path AS inspection_results_file_path, inspection_results.status AS inspection_results_status, inspection_results.cpu_usage AS inspection_results_cpu_usage, inspection_results.memory_usage AS inspection_results_memory_usage, inspection_results.interface_status AS inspection_results_interface_status, inspection_results.summary AS inspection_results_summary, inspection_results.file_exists AS inspection_results_file_exists, inspection_results.created_at AS inspection_results_created_at, inspection_results.updated_at AS inspection_results_updated_at 
FROM inspection_results ORDER BY inspection_results.inspection_time DESC 
 LIMIT %(param_1)s
2025-08-03 12:16:14,462 [INFO] [generated in 0.00069s] {'param_1': 50}
2025-08-03 12:16:14,467 [INFO] SELECT count(inspection_results.id) AS count_1 
FROM inspection_results 
WHERE inspection_results.status = %(status_1)s AND inspection_results.inspection_time >= %(inspection_time_1)s
2025-08-03 12:16:14,467 [INFO] [generated in 0.00047s] {'status_1': 'failure', 'inspection_time_1': datetime.datetime(2025, 7, 27, 12, 16, 14, 466115)}
2025-08-03 12:16:14,468 [INFO] ROLLBACK
2025-08-03 12:16:14,469 [INFO] [**********.454862] 完成请求: GET http://localhost:5888/api/ai-operations/health-metrics - 状态码: 200 - 耗时: 0.0146s
2025-08-03 12:16:14,485 [INFO] [**********.485527] 开始请求: GET http://localhost:5888/api/ai-operations/alerts
2025-08-03 12:16:14,487 [INFO] BEGIN (implicit)
2025-08-03 12:16:14,488 [INFO] SELECT inspection_results.id AS inspection_results_id, inspection_results.device_id AS inspection_results_device_id, inspection_results.inspection_time AS inspection_results_inspection_time, inspection_results.file_path AS inspection_results_file_path, inspection_results.status AS inspection_results_status, inspection_results.cpu_usage AS inspection_results_cpu_usage, inspection_results.memory_usage AS inspection_results_memory_usage, inspection_results.interface_status AS inspection_results_interface_status, inspection_results.summary AS inspection_results_summary, inspection_results.file_exists AS inspection_results_file_exists, inspection_results.created_at AS inspection_results_created_at, inspection_results.updated_at AS inspection_results_updated_at, devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM inspection_results INNER JOIN devices ON inspection_results.device_id = devices.id 
WHERE inspection_results.status = %(status_1)s ORDER BY inspection_results.inspection_time DESC 
 LIMIT %(param_1)s
2025-08-03 12:16:14,490 [INFO] [generated in 0.00078s] {'status_1': 'failure', 'param_1': 10}
2025-08-03 12:16:14,491 [INFO] ROLLBACK
2025-08-03 12:16:14,493 [INFO] [**********.485527] 完成请求: GET http://localhost:5888/api/ai-operations/alerts - 状态码: 200 - 耗时: 0.0077s
2025-08-03 12:16:14,503 [INFO] [**********.503611] 开始请求: GET http://localhost:5888/api/ai-operations/device-predictions
2025-08-03 12:16:14,506 [INFO] BEGIN (implicit)
2025-08-03 12:16:14,507 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.cpu_usage DESC 
 LIMIT %(param_1)s
2025-08-03 12:16:14,508 [INFO] [generated in 0.00112s] {'param_1': 10}
2025-08-03 12:16:14,520 [INFO] ROLLBACK
2025-08-03 12:16:14,522 [INFO] [**********.503611] 完成请求: GET http://localhost:5888/api/ai-operations/device-predictions - 状态码: 200 - 耗时: 0.0192s
2025-08-03 12:16:14,553 [INFO] [**********.553366] 开始请求: GET http://localhost:5888/api/ai-operations/automation-tasks
2025-08-03 12:16:14,556 [INFO] BEGIN (implicit)
2025-08-03 12:16:14,559 [INFO] SELECT count(devices.id) AS count_1 
FROM devices
2025-08-03 12:16:14,559 [INFO] [cached since 0.1031s ago] {}
2025-08-03 12:16:14,564 [INFO] SELECT count(configurations.id) AS count_1 
FROM configurations
2025-08-03 12:16:14,565 [INFO] [generated in 0.00091s] {}
2025-08-03 12:16:14,570 [INFO] SELECT count(devices.id) AS count_1 
FROM devices 
WHERE devices.device_type = %(device_type_1)s
2025-08-03 12:16:14,571 [INFO] [generated in 0.00106s] {'device_type_1': 'switch'}
2025-08-03 12:16:14,576 [INFO] SELECT count(devices.id) AS count_1 
FROM devices 
WHERE devices.device_type = %(device_type_1)s
2025-08-03 12:16:14,576 [INFO] [cached since 0.005952s ago] {'device_type_1': 'router'}
2025-08-03 12:16:14,580 [INFO] ROLLBACK
2025-08-03 12:16:14,583 [INFO] [**********.553366] 完成请求: GET http://localhost:5888/api/ai-operations/automation-tasks - 状态码: 200 - 耗时: 0.0297s
2025-08-03 12:16:14,612 [INFO] [**********.612447] 开始请求: GET http://localhost:5888/api/ai-operations/anomalies
2025-08-03 12:16:14,615 [INFO] BEGIN (implicit)
2025-08-03 12:16:14,617 [INFO] SELECT inspection_results.id AS inspection_results_id, inspection_results.device_id AS inspection_results_device_id, inspection_results.inspection_time AS inspection_results_inspection_time, inspection_results.file_path AS inspection_results_file_path, inspection_results.status AS inspection_results_status, inspection_results.cpu_usage AS inspection_results_cpu_usage, inspection_results.memory_usage AS inspection_results_memory_usage, inspection_results.interface_status AS inspection_results_interface_status, inspection_results.summary AS inspection_results_summary, inspection_results.file_exists AS inspection_results_file_exists, inspection_results.created_at AS inspection_results_created_at, inspection_results.updated_at AS inspection_results_updated_at, devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM inspection_results INNER JOIN devices ON inspection_results.device_id = devices.id 
WHERE inspection_results.cpu_usage > %(cpu_usage_1)s OR inspection_results.memory_usage > %(memory_usage_1)s ORDER BY inspection_results.inspection_time DESC 
 LIMIT %(param_1)s
2025-08-03 12:16:14,618 [INFO] [generated in 0.00087s] {'cpu_usage_1': 85, 'memory_usage_1': 85, 'param_1': 5}
2025-08-03 12:16:14,623 [INFO] SELECT count(inspection_results.id) AS count_1 
FROM inspection_results 
WHERE inspection_results.inspection_time >= %(inspection_time_1)s
2025-08-03 12:16:14,624 [INFO] [generated in 0.00137s] {'inspection_time_1': datetime.datetime(2025, 8, 2, 12, 16, 14, 621344)}
2025-08-03 12:16:14,626 [INFO] ROLLBACK
2025-08-03 12:16:14,628 [INFO] [**********.612447] 完成请求: GET http://localhost:5888/api/ai-operations/anomalies - 状态码: 200 - 耗时: 0.0158s
2025-08-03 12:16:15,616 [INFO] [**********.616345] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:16:15,617 [INFO] [**********.617364] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:16:15,618 [INFO] [**********.617364] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:16:15,618 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,619 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:15,619 [INFO] [cached since 13.54s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:15,621 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,621 [INFO] [cached since 13.52s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:15,623 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,623 [INFO] [cached since 13.52s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:15,626 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,627 [INFO] [cached since 13.53s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:15,628 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,629 [INFO] [**********.629339] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:16:15,629 [INFO] [cached since 13.52s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:15,631 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,633 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,633 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:15,633 [INFO] [cached since 13.53s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:15,633 [INFO] [cached since 13.5s ago] {}
2025-08-03 12:16:15,635 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,636 [INFO] [cached since 13.53s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:15,636 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:15,636 [INFO] [generated in 0.00050s] {}
2025-08-03 12:16:15,638 [INFO] ROLLBACK
2025-08-03 12:16:15,640 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:15,641 [INFO] [generated in 0.00077s] {}
2025-08-03 12:16:15,642 [INFO] [**********.616345] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0264s
2025-08-03 12:16:15,644 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:16:15,646 [INFO] [generated in 0.00197s] {}
2025-08-03 12:16:15,648 [INFO] [**********.648416] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:16:15,651 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:16:15,654 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,654 [INFO] [generated in 0.00298s] {}
2025-08-03 12:16:15,655 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:15,656 [INFO] [cached since 13.57s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:15,659 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,660 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:16:15,660 [INFO] [cached since 13.56s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:15,661 [INFO] [generated in 0.00122s] {}
2025-08-03 12:16:15,664 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,666 [INFO] [cached since 13.56s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:15,667 [INFO] ROLLBACK
2025-08-03 12:16:15,669 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,672 [INFO] [**********.629339] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0429s
2025-08-03 12:16:15,671 [INFO] [cached since 13.57s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:15,674 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,675 [INFO] [cached since 13.57s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:15,678 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,678 [INFO] [**********.678365] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:16:15,679 [INFO] [cached since 13.58s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:15,682 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,683 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:15,683 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:15,684 [INFO] [cached since 13.58s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:15,684 [INFO] [cached since 13.55s ago] {}
2025-08-03 12:16:15,686 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:15,687 [INFO] ROLLBACK
2025-08-03 12:16:15,687 [INFO] [cached since 0.05174s ago] {}
2025-08-03 12:16:15,689 [INFO] [**********.648416] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0410s
2025-08-03 12:16:15,689 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:15,690 [INFO] [cached since 0.05086s ago] {}
2025-08-03 12:16:15,691 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:16:15,691 [INFO] [generated in 0.00037s] {}
2025-08-03 12:16:15,693 [INFO] ROLLBACK
2025-08-03 12:16:15,695 [INFO] [**********.678365] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0170s
2025-08-03 12:16:15,702 [INFO] [**********.702132] 开始请求: POST http://localhost:5888/api/rack-management/debug
2025-08-03 12:16:15,703 [INFO] [**********.702132] 完成请求: POST http://localhost:5888/api/rack-management/debug - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:16:15,713 [INFO] [**********.712987] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:16:15,714 [INFO] [**********.712987] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0016s
2025-08-03 12:16:15,724 [INFO] [**********.724862] 开始请求: GET http://localhost:5888/api/rack-management/
2025-08-03 12:16:15,729 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,730 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:15,730 [INFO] [cached since 13.6s ago] {}
2025-08-03 12:16:15,737 [INFO] 
            SELECT id, name, location, total_u, used_u, width, depth, 
                  temperature, humidity, power, max_power, status, description,
                  created_at, updated_at
            FROM racks
            LIMIT %(limit)s OFFSET %(skip)s
        
2025-08-03 12:16:15,739 [INFO] [cached since 13.6s ago] {'limit': 100, 'skip': 0}
2025-08-03 12:16:15,744 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:15,745 [INFO] [cached since 13.6s ago] {'rack_id': 'A01'}
2025-08-03 12:16:15,756 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:15,757 [INFO] [cached since 13.62s ago] {'rack_id': 'A02'}
2025-08-03 12:16:15,763 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:15,768 [INFO] [cached since 13.63s ago] {'rack_id': 'B01'}
2025-08-03 12:16:15,781 [INFO] ROLLBACK
2025-08-03 12:16:15,785 [INFO] [**********.724862] 完成请求: GET http://localhost:5888/api/rack-management/ - 状态码: 200 - 耗时: 0.0603s
2025-08-03 12:16:15,831 [INFO] [**********.831028] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:16:15,835 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,837 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:15,838 [INFO] [cached since 13.7s ago] {}
2025-08-03 12:16:15,842 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:15,843 [INFO] [cached since 0.2066s ago] {}
2025-08-03 12:16:15,848 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:15,850 [INFO] [cached since 0.2103s ago] {}
2025-08-03 12:16:15,853 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:16:15,855 [INFO] [cached since 0.1632s ago] {}
2025-08-03 12:16:15,859 [INFO] ROLLBACK
2025-08-03 12:16:15,861 [INFO] [**********.831028] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0307s
2025-08-03 12:16:15,873 [INFO] [**********.873802] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:16:15,877 [INFO] BEGIN (implicit)
2025-08-03 12:16:15,878 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:15,879 [INFO] [cached since 13.74s ago] {}
2025-08-03 12:16:15,883 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:15,883 [INFO] [cached since 0.2472s ago] {}
2025-08-03 12:16:15,889 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:15,890 [INFO] [cached since 0.2501s ago] {}
2025-08-03 12:16:15,893 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:16:15,893 [INFO] [cached since 0.2505s ago] {}
2025-08-03 12:16:15,895 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:16:15,896 [INFO] [cached since 0.2455s ago] {}
2025-08-03 12:16:15,901 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:16:15,901 [INFO] [cached since 0.2425s ago] {}
2025-08-03 12:16:15,907 [INFO] ROLLBACK
2025-08-03 12:16:15,912 [INFO] [**********.873802] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0385s
2025-08-03 12:16:19,213 [INFO] [**********.213177] 开始请求: GET http://localhost:5888/api/devices/stats
2025-08-03 12:16:19,214 [INFO] [**********.214184] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=50
2025-08-03 12:16:19,215 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,215 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,216 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,216 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,216 [INFO] [cached since 33.38s ago] {}
2025-08-03 12:16:19,217 [INFO] [cached since 33.38s ago] {}
2025-08-03 12:16:19,219 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:19,220 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:16:19,221 [INFO] [cached since 33.36s ago] {'param_1': 0, 'param_2': 50}
2025-08-03 12:16:19,221 [INFO] [cached since 33.37s ago] {'device_type_1_1': 'switch', 'device_type_1_2': 'router', 'device_type_1_3': '交换机', 'device_type_1_4': '路由器'}
2025-08-03 12:16:19,222 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:16:19,224 [INFO] [cached since 33.37s ago] {'device_type_1_1': 'firewall', 'device_type_1_2': 'security', 'device_type_1_3': '防火墙', 'device_type_1_4': '安全设备'}
2025-08-03 12:16:19,226 [INFO] ROLLBACK
2025-08-03 12:16:19,227 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.is_active = true) AS anon_1
2025-08-03 12:16:19,228 [INFO] [**********.214184] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=50 - 状态码: 200 - 耗时: 0.0138s
2025-08-03 12:16:19,228 [INFO] [cached since 33.35s ago] {}
2025-08-03 12:16:19,229 [INFO] SELECT devices.manufacturer AS devices_manufacturer, count(devices.id) AS count 
FROM devices 
WHERE devices.manufacturer IS NOT NULL AND devices.manufacturer != %(manufacturer_1)s GROUP BY devices.manufacturer
2025-08-03 12:16:19,230 [INFO] [cached since 33.34s ago] {'manufacturer_1': ''}
2025-08-03 12:16:19,231 [INFO] [**********.231138] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=20
2025-08-03 12:16:19,240 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,241 [INFO] ROLLBACK
2025-08-03 12:16:19,242 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,243 [INFO] [cached since 33.4s ago] {}
2025-08-03 12:16:19,244 [INFO] [**********.213177] 完成请求: GET http://localhost:5888/api/devices/stats - 状态码: 200 - 耗时: 0.0316s
2025-08-03 12:16:19,248 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:19,249 [INFO] [**********.24987] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:16:19,250 [INFO] [cached since 33.39s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:16:19,277 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,281 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,285 [INFO] [cached since 33.44s ago] {}
2025-08-03 12:16:19,287 [INFO] ROLLBACK
2025-08-03 12:16:19,290 [INFO] [**********.231138] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0594s
2025-08-03 12:16:19,290 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:19,292 [INFO] [cached since 33.43s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:19,296 [INFO] [**********.296112] 开始请求: GET http://localhost:5888/api/devices/ports/statistics
2025-08-03 12:16:19,299 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,301 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:16:19,304 [INFO] ROLLBACK
2025-08-03 12:16:19,304 [INFO] [cached since 37.42s ago] {}
2025-08-03 12:16:19,308 [INFO] [**********.24987] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0580s
2025-08-03 12:16:19,315 [INFO] ROLLBACK
2025-08-03 12:16:19,318 [INFO] [**********.318539] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:16:19,322 [INFO] [**********.296112] 完成请求: GET http://localhost:5888/api/devices/ports/statistics - 状态码: 200 - 耗时: 0.0269s
2025-08-03 12:16:19,325 [INFO] [**********.318539] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0071s
2025-08-03 12:16:19,330 [INFO] [**********.33087] 开始请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20
2025-08-03 12:16:19,332 [INFO] [**********.332919] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:16:19,337 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,338 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,339 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:19,340 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,340 [INFO] [cached since 33.35s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:16:19,342 [INFO] [cached since 33.5s ago] {}
2025-08-03 12:16:19,345 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,348 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:19,349 [INFO] [cached since 33.51s ago] {}
2025-08-03 12:16:19,349 [INFO] [cached since 33.49s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:19,361 [INFO] ROLLBACK
2025-08-03 12:16:19,363 [INFO] ROLLBACK
2025-08-03 12:16:19,367 [INFO] [**********.332919] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0348s
2025-08-03 12:16:19,369 [INFO] [**********.33087] 完成请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0385s
2025-08-03 12:16:19,375 [INFO] [**********.375132] 开始请求: GET http://localhost:5888/api/ip-management/statistics
2025-08-03 12:16:19,377 [INFO] [**********.377578] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=1
2025-08-03 12:16:19,381 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,381 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,382 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:16:19,384 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:16:19,385 [INFO] [cached since 33.54s ago] {}
2025-08-03 12:16:19,385 [INFO] [cached since 37.5s ago] {}
2025-08-03 12:16:19,388 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:19,393 [INFO] ROLLBACK
2025-08-03 12:16:19,393 [INFO] [cached since 33.53s ago] {'param_1': 0, 'param_2': 1}
2025-08-03 12:16:19,396 [INFO] [**********.375132] 完成请求: GET http://localhost:5888/api/ip-management/statistics - 状态码: 200 - 耗时: 0.0215s
2025-08-03 12:16:19,399 [INFO] ROLLBACK
2025-08-03 12:16:19,400 [INFO] [**********.377578] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=1 - 状态码: 200 - 耗时: 0.0230s
2025-08-03 12:16:19,432 [INFO] [**********.432967] 开始请求: GET http://localhost:5888/api/rack-management/statistics
2025-08-03 12:16:19,434 [INFO] [**********.434985] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:16:19,436 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,436 [INFO] BEGIN (implicit)
2025-08-03 12:16:19,438 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:16:19,439 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:16:19,439 [INFO] [cached since 34.27s ago] {'param_1': 1}
2025-08-03 12:16:19,439 [INFO] [cached since 37.56s ago] {}
2025-08-03 12:16:19,445 [INFO] ROLLBACK
2025-08-03 12:16:19,445 [INFO] ROLLBACK
2025-08-03 12:16:19,447 [INFO] [**********.434985] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.0123s
2025-08-03 12:16:19,449 [INFO] [**********.432967] 完成请求: GET http://localhost:5888/api/rack-management/statistics - 状态码: 200 - 耗时: 0.0166s
2025-08-03 12:16:21,405 [INFO] [**********.405287] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:16:21,406 [INFO] [**********.406316] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:16:21,407 [INFO] [**********.406316] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:16:21,407 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,408 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:21,409 [INFO] [cached since 19.33s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:21,409 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,410 [INFO] [cached since 19.31s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:21,411 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,411 [INFO] [cached since 19.31s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:21,413 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,413 [INFO] [cached since 19.31s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:21,415 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,415 [INFO] [cached since 19.31s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:21,417 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,417 [INFO] [cached since 19.32s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:21,419 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,419 [INFO] [cached since 19.31s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:21,421 [INFO] ROLLBACK
2025-08-03 12:16:21,421 [INFO] [**********.405287] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0163s
2025-08-03 12:16:21,423 [INFO] [**********.423669] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:16:21,425 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,426 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:21,426 [INFO] [cached since 19.34s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:21,428 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,428 [INFO] [**********.428644] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:16:21,429 [INFO] [cached since 19.33s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:21,430 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,431 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,432 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:21,432 [INFO] [cached since 19.33s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:21,432 [INFO] [cached since 19.3s ago] {}
2025-08-03 12:16:21,434 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,434 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:21,434 [INFO] [cached since 19.34s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:21,434 [INFO] [cached since 5.799s ago] {}
2025-08-03 12:16:21,438 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,439 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:21,439 [INFO] [cached since 19.33s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:21,439 [INFO] [cached since 5.8s ago] {}
2025-08-03 12:16:21,440 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,441 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:16:21,441 [INFO] [cached since 19.34s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:21,442 [INFO] [cached since 5.799s ago] {}
2025-08-03 12:16:21,446 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:21,447 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:16:21,447 [INFO] [cached since 19.34s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:21,448 [INFO] [cached since 5.797s ago] {}
2025-08-03 12:16:21,451 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:16:21,451 [INFO] [cached since 5.792s ago] {}
2025-08-03 12:16:21,453 [INFO] ROLLBACK
2025-08-03 12:16:21,455 [INFO] [**********.423669] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0321s
2025-08-03 12:16:21,457 [INFO] ROLLBACK
2025-08-03 12:16:21,460 [INFO] [**********.428644] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0321s
2025-08-03 12:16:21,471 [INFO] [**********.471756] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:16:21,474 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,475 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:21,475 [INFO] [cached since 19.34s ago] {}
2025-08-03 12:16:21,477 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:21,478 [INFO] [cached since 5.841s ago] {}
2025-08-03 12:16:21,481 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:21,481 [INFO] [cached since 5.842s ago] {}
2025-08-03 12:16:21,482 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:16:21,483 [INFO] [cached since 5.792s ago] {}
2025-08-03 12:16:21,484 [INFO] ROLLBACK
2025-08-03 12:16:21,485 [INFO] [**********.471756] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0141s
2025-08-03 12:16:21,490 [INFO] [**********.490483] 开始请求: POST http://localhost:5888/api/rack-management/debug
2025-08-03 12:16:21,493 [INFO] [**********.490483] 完成请求: POST http://localhost:5888/api/rack-management/debug - 状态码: 200 - 耗时: 0.0030s
2025-08-03 12:16:21,505 [INFO] [**********.504715] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:16:21,506 [INFO] [**********.504715] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0022s
2025-08-03 12:16:21,511 [INFO] [**********.511715] 开始请求: GET http://localhost:5888/api/rack-management/
2025-08-03 12:16:21,513 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,513 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:21,514 [INFO] [cached since 19.38s ago] {}
2025-08-03 12:16:21,517 [INFO] 
            SELECT id, name, location, total_u, used_u, width, depth, 
                  temperature, humidity, power, max_power, status, description,
                  created_at, updated_at
            FROM racks
            LIMIT %(limit)s OFFSET %(skip)s
        
2025-08-03 12:16:21,518 [INFO] [cached since 19.38s ago] {'limit': 100, 'skip': 0}
2025-08-03 12:16:21,519 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:21,519 [INFO] [cached since 19.38s ago] {'rack_id': 'A01'}
2025-08-03 12:16:21,521 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:21,521 [INFO] [cached since 19.38s ago] {'rack_id': 'A02'}
2025-08-03 12:16:21,523 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:16:21,524 [INFO] [cached since 19.38s ago] {'rack_id': 'B01'}
2025-08-03 12:16:21,527 [INFO] ROLLBACK
2025-08-03 12:16:21,529 [INFO] [**********.511715] 完成请求: GET http://localhost:5888/api/rack-management/ - 状态码: 200 - 耗时: 0.0174s
2025-08-03 12:16:21,565 [INFO] [**********.56553] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:16:21,569 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,570 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:21,570 [INFO] [cached since 19.43s ago] {}
2025-08-03 12:16:21,575 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:21,576 [INFO] [cached since 5.94s ago] {}
2025-08-03 12:16:21,581 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:21,582 [INFO] [cached since 5.942s ago] {}
2025-08-03 12:16:21,584 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:16:21,584 [INFO] [cached since 5.893s ago] {}
2025-08-03 12:16:21,587 [INFO] ROLLBACK
2025-08-03 12:16:21,589 [INFO] [**********.56553] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0242s
2025-08-03 12:16:21,603 [INFO] [**********.602991] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:16:21,606 [INFO] BEGIN (implicit)
2025-08-03 12:16:21,607 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:16:21,608 [INFO] [cached since 19.47s ago] {}
2025-08-03 12:16:21,612 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:16:21,612 [INFO] [cached since 5.976s ago] {}
2025-08-03 12:16:21,617 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:16:21,617 [INFO] [cached since 5.978s ago] {}
2025-08-03 12:16:21,620 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:16:21,620 [INFO] [cached since 5.977s ago] {}
2025-08-03 12:16:21,624 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:16:21,624 [INFO] [cached since 5.973s ago] {}
2025-08-03 12:16:21,628 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:16:21,628 [INFO] [cached since 5.969s ago] {}
2025-08-03 12:16:21,632 [INFO] ROLLBACK
2025-08-03 12:16:21,636 [INFO] [**********.602991] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0333s
2025-08-03 12:16:22,630 [INFO] [**********.63013] 开始请求: GET http://localhost:5888/api/rack-management/A01
2025-08-03 12:16:22,631 [INFO] [**********.631104] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:16:22,633 [INFO] BEGIN (implicit)
2025-08-03 12:16:22,633 [INFO] BEGIN (implicit)
2025-08-03 12:16:22,633 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:16:22,634 [INFO] SELECT racks.id AS racks_id 
FROM racks 
WHERE racks.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:16:22,634 [INFO] [cached since 20.55s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:16:22,634 [INFO] [generated in 0.00055s] {'id_1': 'A01', 'param_1': 1}
2025-08-03 12:16:22,635 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:22,636 [INFO] 
            SELECT id, name, location, datacenter_id, total_u, used_u, width, depth, 
                   temperature, humidity, power, max_power, status, description,
                   created_at, updated_at
            FROM racks
            WHERE id = %(rack_id)s
        
2025-08-03 12:16:22,636 [INFO] [cached since 20.54s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:22,637 [INFO] [generated in 0.00062s] {'rack_id': 'A01'}
2025-08-03 12:16:22,638 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:22,639 [INFO] [cached since 20.53s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:16:22,639 [INFO] 
            SELECT id, name, type, model, position, u_size, power,
                   weight, status, temperature, description, rack_id,
                   serial_number, ip_address, created_at, updated_at
            FROM rack_devices
            WHERE rack_id = %(rack_id)s
        
2025-08-03 12:16:22,640 [INFO] [generated in 0.00067s] {'rack_id': 'A01'}
2025-08-03 12:16:22,641 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:22,641 [INFO] [cached since 20.54s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:22,642 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:22,643 [INFO] [cached since 20.54s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:16:22,644 [INFO] ROLLBACK
2025-08-03 12:16:22,645 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:22,646 [INFO] [**********.63013] 完成请求: GET http://localhost:5888/api/rack-management/A01 - 状态码: 200 - 耗时: 0.0164s
2025-08-03 12:16:22,647 [INFO] [cached since 20.55s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:22,648 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:16:22,649 [INFO] [cached since 20.54s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:16:22,650 [INFO] ROLLBACK
2025-08-03 12:16:22,651 [INFO] [**********.631104] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0207s
2025-08-03 12:25:48,214 [INFO] [**********.214786] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:25:48,215 [INFO] [**********.215303] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:25:48,216 [INFO] [**********.215303] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0015s
2025-08-03 12:25:48,217 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,218 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:25:48,218 [INFO] [cached since 586.1s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:25:48,221 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,221 [INFO] [**********.221605] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:25:48,221 [INFO] [cached since 586.1s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:25:48,222 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,223 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,224 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:25:48,224 [INFO] [cached since 586.1s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:25:48,224 [INFO] [cached since 586.1s ago] {}
2025-08-03 12:25:48,225 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,226 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:25:48,226 [INFO] [cached since 586.1s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:25:48,226 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,227 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,228 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:25:48,228 [INFO] [cached since 586.1s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:25:48,228 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,230 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,231 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:25:48,231 [INFO] [cached since 586.1s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:25:48,231 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,233 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,234 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:25:48,234 [INFO] [cached since 586.1s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:25:48,234 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,237 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:25:48,238 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,239 [INFO] ROLLBACK
2025-08-03 12:25:48,241 [INFO] [**********.214786] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0266s
2025-08-03 12:25:48,243 [INFO] ROLLBACK
2025-08-03 12:25:48,244 [INFO] [**********.244563] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:25:48,247 [INFO] [**********.221605] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0254s
2025-08-03 12:25:48,249 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,250 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:25:48,251 [INFO] [cached since 586.2s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:25:48,252 [INFO] [**********.25299] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:25:48,254 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,255 [INFO] [cached since 586.2s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:25:48,256 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,256 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:25:48,258 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,258 [INFO] [cached since 586.1s ago] {}
2025-08-03 12:25:48,258 [INFO] [cached since 586.2s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:25:48,260 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,260 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:25:48,260 [INFO] [cached since 586.2s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:25:48,260 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,262 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,262 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:25:48,262 [INFO] [cached since 586.2s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:25:48,263 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,264 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:25:48,266 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,266 [INFO] [cached since 572.6s ago] {}
2025-08-03 12:25:48,266 [INFO] [cached since 586.2s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:25:48,268 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:25:48,268 [INFO] ROLLBACK
2025-08-03 12:25:48,268 [INFO] [cached since 586.2s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:25:48,270 [INFO] [**********.25299] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0162s
2025-08-03 12:25:48,272 [INFO] ROLLBACK
2025-08-03 12:25:48,273 [INFO] [**********.244563] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0289s
2025-08-03 12:25:48,274 [INFO] [**********.274467] 开始请求: POST http://localhost:5888/api/rack-management/debug
2025-08-03 12:25:48,275 [INFO] [**********.274467] 完成请求: POST http://localhost:5888/api/rack-management/debug - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:25:48,282 [INFO] [**********.282609] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:25:48,285 [INFO] [**********.282609] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0025s
2025-08-03 12:25:48,291 [INFO] [**********.291899] 开始请求: GET http://localhost:5888/api/rack-management/
2025-08-03 12:25:48,293 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,293 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:25:48,293 [INFO] [cached since 586.2s ago] {}
2025-08-03 12:25:48,295 [INFO] 
            SELECT id, name, location, total_u, used_u, width, depth, 
                  temperature, humidity, power, max_power, status, description,
                  created_at, updated_at
            FROM racks
            LIMIT %(limit)s OFFSET %(skip)s
        
2025-08-03 12:25:48,296 [INFO] [cached since 586.2s ago] {'limit': 100, 'skip': 0}
2025-08-03 12:25:48,298 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:25:48,299 [INFO] [cached since 586.2s ago] {'rack_id': 'A01'}
2025-08-03 12:25:48,301 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:25:48,302 [INFO] [cached since 586.2s ago] {'rack_id': 'A02'}
2025-08-03 12:25:48,303 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:25:48,304 [INFO] [cached since 586.2s ago] {'rack_id': 'B01'}
2025-08-03 12:25:48,308 [INFO] ROLLBACK
2025-08-03 12:25:48,309 [INFO] [**********.291899] 完成请求: GET http://localhost:5888/api/rack-management/ - 状态码: 200 - 耗时: 0.0173s
2025-08-03 12:25:48,345 [INFO] [**********.345872] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:25:48,346 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,347 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:25:48,348 [INFO] [cached since 586.2s ago] {}
2025-08-03 12:25:48,351 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:25:48,352 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,353 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:25:48,354 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,355 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:25:48,355 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,356 [INFO] ROLLBACK
2025-08-03 12:25:48,360 [INFO] [**********.345872] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0135s
2025-08-03 12:25:48,372 [INFO] [**********.372539] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:25:48,376 [INFO] BEGIN (implicit)
2025-08-03 12:25:48,377 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:25:48,377 [INFO] [cached since 586.2s ago] {}
2025-08-03 12:25:48,380 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:25:48,380 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,382 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:25:48,382 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,383 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:25:48,384 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,385 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:25:48,385 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,386 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:25:48,387 [INFO] [cached since 572.7s ago] {}
2025-08-03 12:25:48,388 [INFO] ROLLBACK
2025-08-03 12:25:48,389 [INFO] [**********.372539] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0174s
2025-08-03 12:33:21,604 [INFO] [**********.604044] 开始请求: GET http://localhost:5888/api/ai/health
2025-08-03 12:33:21,605 [INFO] [**********.605098] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:33:21,605 [INFO] [**********.605098] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:33:21,870 [INFO] [**********.604044] 完成请求: GET http://localhost:5888/api/ai/health - 状态码: 200 - 耗时: 0.2662s
2025-08-03 12:33:21,870 [INFO] [**********.605098] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.2657s
2025-08-03 12:33:21,871 [INFO] [**********.605098] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.2662s
2025-08-03 12:33:21,872 [INFO] [**********.87231] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:33:22,008 [INFO] [**********.87231] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1362s
2025-08-03 12:33:22,009 [INFO] [**********.00959] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:33:22,009 [INFO] [**********.00959] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:33:22,011 [INFO] [**********.00959] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0015s
2025-08-03 12:33:22,012 [INFO] [**********.01215] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:33:22,119 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,120 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:33:22,120 [INFO] [cached since 1057s ago] {'param_1': 1}
2025-08-03 12:33:22,122 [INFO] [**********.122023] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:33:22,123 [INFO] [**********.01215] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1109s
2025-08-03 12:33:22,125 [INFO] ROLLBACK
2025-08-03 12:33:22,126 [INFO] [**********.122023] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0042s
2025-08-03 12:33:22,127 [INFO] [**********.12721] 开始请求: POST http://localhost:5888/api/license/record-login
2025-08-03 12:33:22,128 [INFO] [**********.00959] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.1187s
2025-08-03 12:33:22,130 [INFO] [**********.12721] 完成请求: POST http://localhost:5888/api/license/record-login - 状态码: 200 - 耗时: 0.0029s
2025-08-03 12:33:22,132 [INFO] [**********.132447] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:33:22,273 [INFO] [**********.132447] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.1406s
2025-08-03 12:33:22,276 [INFO] [**********.276196] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:33:22,421 [INFO] [**********.276196] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.1454s
2025-08-03 12:33:22,563 [INFO] [**********.563504] 开始请求: GET http://localhost:5888/api/devices/stats
2025-08-03 12:33:22,563 [INFO] [**********.563504] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=50
2025-08-03 12:33:22,565 [INFO] [**********.565534] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=20
2025-08-03 12:33:22,565 [INFO] [**********.565534] 开始请求: GET http://localhost:5888/api/devices/ports/statistics
2025-08-03 12:33:22,565 [INFO] [**********.565534] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:33:22,569 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,570 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,571 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,571 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,572 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,572 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,572 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,573 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,573 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:33:22,574 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,574 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,575 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,575 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,575 [INFO] [cached since 1061s ago] {}
2025-08-03 12:33:22,576 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,578 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:22,579 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:33:22,580 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:22,581 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:22,583 [INFO] [cached since 1057s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:33:22,585 [INFO] [cached since 1057s ago] {'device_type_1_1': 'switch', 'device_type_1_2': 'router', 'device_type_1_3': '交换机', 'device_type_1_4': '路由器'}
2025-08-03 12:33:22,585 [INFO] ROLLBACK
2025-08-03 12:33:22,586 [INFO] [cached since 1057s ago] {'param_1': 0, 'param_2': 50}
2025-08-03 12:33:22,587 [INFO] [cached since 1057s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:33:22,591 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.device_type IN (%(device_type_1_1)s, %(device_type_1_2)s, %(device_type_1_3)s, %(device_type_1_4)s)) AS anon_1
2025-08-03 12:33:22,598 [INFO] [**********.565534] 完成请求: GET http://localhost:5888/api/devices/ports/statistics - 状态码: 200 - 耗时: 0.0326s
2025-08-03 12:33:22,599 [INFO] [cached since 1057s ago] {'device_type_1_1': 'firewall', 'device_type_1_2': 'security', 'device_type_1_3': '防火墙', 'device_type_1_4': '安全设备'}
2025-08-03 12:33:22,603 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
WHERE devices.is_active = true) AS anon_1
2025-08-03 12:33:22,605 [INFO] [**********.605395] 开始请求: GET http://localhost:5888/api/system/health
2025-08-03 12:33:22,607 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,609 [INFO] ROLLBACK
2025-08-03 12:33:22,615 [INFO] SELECT devices.manufacturer AS devices_manufacturer, count(devices.id) AS count 
FROM devices 
WHERE devices.manufacturer IS NOT NULL AND devices.manufacturer != %(manufacturer_1)s GROUP BY devices.manufacturer
2025-08-03 12:33:22,617 [INFO] [**********.605395] 完成请求: GET http://localhost:5888/api/system/health - 状态码: 200 - 耗时: 0.0117s
2025-08-03 12:33:22,617 [INFO] [cached since 1057s ago] {'manufacturer_1': ''}
2025-08-03 12:33:22,622 [INFO] [**********.565534] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0574s
2025-08-03 12:33:22,622 [INFO] ROLLBACK
2025-08-03 12:33:22,623 [INFO] ROLLBACK
2025-08-03 12:33:22,624 [INFO] [**********.624488] 开始请求: GET http://localhost:5888/api/ip-management/statistics
2025-08-03 12:33:22,626 [INFO] [**********.626907] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=1
2025-08-03 12:33:22,628 [INFO] [**********.565534] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0629s
2025-08-03 12:33:22,629 [INFO] ROLLBACK
2025-08-03 12:33:22,629 [INFO] [**********.563504] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=50 - 状态码: 200 - 耗时: 0.0660s
2025-08-03 12:33:22,632 [INFO] [**********.563504] 完成请求: GET http://localhost:5888/api/devices/stats - 状态码: 200 - 耗时: 0.0690s
2025-08-03 12:33:22,632 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,633 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:33:22,634 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,634 [INFO] [cached since 1061s ago] {}
2025-08-03 12:33:22,635 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,635 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,639 [INFO] [**********.639478] 开始请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20
2025-08-03 12:33:22,640 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:22,641 [INFO] ROLLBACK
2025-08-03 12:33:22,641 [INFO] [cached since 1057s ago] {'param_1': 0, 'param_2': 1}
2025-08-03 12:33:22,641 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,644 [INFO] [**********.624488] 完成请求: GET http://localhost:5888/api/ip-management/statistics - 状态码: 200 - 耗时: 0.0195s
2025-08-03 12:33:22,642 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:22,645 [INFO] [cached since 1057s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 12:33:22,646 [INFO] [**********.64633] 开始请求: GET http://localhost:5888/api/devices/?skip=0&limit=100
2025-08-03 12:33:22,648 [INFO] ROLLBACK
2025-08-03 12:33:22,648 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,650 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,651 [INFO] [**********.651662] 开始请求: GET http://localhost:5888/api/rack-management/statistics
2025-08-03 12:33:22,652 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,653 [INFO] SELECT count(*) AS count_1 
FROM (SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices) AS anon_1
2025-08-03 12:33:22,654 [INFO] [**********.626907] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=1 - 状态码: 200 - 耗时: 0.0273s
2025-08-03 12:33:22,654 [INFO] [cached since 1057s ago] {}
2025-08-03 12:33:22,657 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices ORDER BY devices.is_active DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:22,658 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,658 [INFO] [cached since 1057s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:33:22,660 [INFO] ROLLBACK
2025-08-03 12:33:22,660 [INFO] SELECT devices.id AS devices_id, devices.name AS devices_name, devices.ip_address AS devices_ip_address, devices.device_type AS devices_device_type, devices.manufacturer AS devices_manufacturer, devices.model AS devices_model, devices.username AS devices_username, devices.password AS devices_password, devices.is_active AS devices_is_active, devices.created_at AS devices_created_at, devices.updated_at AS devices_updated_at, devices.protocol AS devices_protocol, devices.port AS devices_port, devices.netmiko_device_type AS devices_netmiko_device_type, devices.last_check_time AS devices_last_check_time, devices.status AS devices_status, devices.cpu_usage AS devices_cpu_usage, devices.memory_usage AS devices_memory_usage, devices.version_info AS devices_version_info, devices.datacenter AS devices_datacenter, devices.rack AS devices_rack, devices.position AS devices_position, devices.serial_number AS devices_serial_number, devices.primary_mac AS devices_primary_mac, devices.additional_macs AS devices_additional_macs, devices.mac_status AS devices_mac_status, devices.mac_last_updated AS devices_mac_last_updated 
FROM devices
2025-08-03 12:33:22,663 [INFO] [**********.663196] 开始请求: GET http://localhost:5888/api/ai/settings
2025-08-03 12:33:22,661 [INFO] [cached since 1061s ago] {}
2025-08-03 12:33:22,665 [INFO] [**********.639478] 完成请求: GET http://localhost:5888/api/inspection-results/?skip=0&limit=20 - 状态码: 200 - 耗时: 0.0246s
2025-08-03 12:33:22,667 [INFO] BEGIN (implicit)
2025-08-03 12:33:22,670 [INFO] SELECT ai_settings.id AS ai_settings_id, ai_settings.provider AS ai_settings_provider, ai_settings.endpoint AS ai_settings_endpoint, ai_settings.api_key AS ai_settings_api_key, ai_settings.default_model AS ai_settings_default_model, ai_settings.custom_models AS ai_settings_custom_models, ai_settings.system_prompt AS ai_settings_system_prompt, ai_settings.created_at AS ai_settings_created_at, ai_settings.updated_at AS ai_settings_updated_at 
FROM ai_settings 
 LIMIT %(param_1)s
2025-08-03 12:33:22,670 [INFO] [cached since 1057s ago] {'param_1': 1}
2025-08-03 12:33:22,672 [INFO] ROLLBACK
2025-08-03 12:33:22,672 [INFO] ROLLBACK
2025-08-03 12:33:22,674 [INFO] ROLLBACK
2025-08-03 12:33:22,675 [INFO] [**********.663196] 完成请求: GET http://localhost:5888/api/ai/settings - 状态码: 200 - 耗时: 0.0119s
2025-08-03 12:33:22,676 [INFO] [**********.651662] 完成请求: GET http://localhost:5888/api/rack-management/statistics - 状态码: 200 - 耗时: 0.0245s
2025-08-03 12:33:22,676 [INFO] [**********.64633] 完成请求: GET http://localhost:5888/api/devices/?skip=0&limit=100 - 状态码: 200 - 耗时: 0.0303s
2025-08-03 12:33:45,859 [INFO] [**********.859301] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:33:45,860 [INFO] BEGIN (implicit)
2025-08-03 12:33:45,861 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:45,861 [INFO] [cached since 1064s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:33:45,863 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,863 [INFO] [cached since 1064s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:33:45,864 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,865 [INFO] [cached since 1064s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:33:45,866 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,866 [INFO] [cached since 1064s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:33:45,866 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,866 [INFO] [cached since 1064s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:33:45,868 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,868 [INFO] [cached since 1064s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:33:45,870 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,870 [INFO] [cached since 1064s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:33:45,873 [INFO] ROLLBACK
2025-08-03 12:33:45,876 [INFO] [**********.859301] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0173s
2025-08-03 12:33:45,880 [INFO] [**********.879571] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:33:45,882 [INFO] [**********.879571] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0020s
2025-08-03 12:33:45,885 [INFO] [**********.885636] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:33:45,890 [INFO] BEGIN (implicit)
2025-08-03 12:33:45,892 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:45,893 [INFO] [cached since 1064s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:33:45,899 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,900 [INFO] [cached since 1064s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:33:45,903 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,903 [INFO] [cached since 1064s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:33:45,906 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,906 [INFO] [cached since 1064s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:33:45,908 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,909 [INFO] [cached since 1064s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:33:45,910 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,910 [INFO] [cached since 1064s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:33:45,912 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:45,912 [INFO] [cached since 1064s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:33:45,914 [INFO] ROLLBACK
2025-08-03 12:33:45,915 [INFO] [**********.885636] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0300s
2025-08-03 12:33:45,917 [INFO] [**********.917117] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:33:45,919 [INFO] BEGIN (implicit)
2025-08-03 12:33:45,919 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:33:45,919 [INFO] [cached since 1064s ago] {}
2025-08-03 12:33:45,921 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:33:45,921 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,922 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:33:45,922 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,923 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:33:45,923 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,924 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:33:45,924 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,925 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:33:45,925 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,927 [INFO] ROLLBACK
2025-08-03 12:33:45,928 [INFO] [**********.917117] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0111s
2025-08-03 12:33:45,933 [INFO] [**********.932534] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:33:45,934 [INFO] BEGIN (implicit)
2025-08-03 12:33:45,934 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:33:45,934 [INFO] [cached since 1064s ago] {}
2025-08-03 12:33:45,936 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:33:45,936 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,936 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:33:45,938 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,938 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:33:45,938 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:45,939 [INFO] ROLLBACK
2025-08-03 12:33:45,940 [INFO] [**********.932534] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0078s
2025-08-03 12:33:45,945 [INFO] [**********.945352] 开始请求: POST http://localhost:5888/api/rack-management/debug
2025-08-03 12:33:45,947 [INFO] [**********.945352] 完成请求: POST http://localhost:5888/api/rack-management/debug - 状态码: 200 - 耗时: 0.0019s
2025-08-03 12:33:45,955 [INFO] [**********.9551] 开始请求: GET http://localhost:5888/api/rack-management/health-check
2025-08-03 12:33:45,956 [INFO] [**********.9551] 完成请求: GET http://localhost:5888/api/rack-management/health-check - 状态码: 200 - 耗时: 0.0015s
2025-08-03 12:33:45,960 [INFO] [**********.960207] 开始请求: GET http://localhost:5888/api/rack-management/
2025-08-03 12:33:45,962 [INFO] BEGIN (implicit)
2025-08-03 12:33:45,962 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:33:45,963 [INFO] [cached since 1064s ago] {}
2025-08-03 12:33:45,965 [INFO] 
            SELECT id, name, location, total_u, used_u, width, depth, 
                  temperature, humidity, power, max_power, status, description,
                  created_at, updated_at
            FROM racks
            LIMIT %(limit)s OFFSET %(skip)s
        
2025-08-03 12:33:45,965 [INFO] [cached since 1064s ago] {'limit': 100, 'skip': 0}
2025-08-03 12:33:45,968 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:33:45,969 [INFO] [cached since 1064s ago] {'rack_id': 'A01'}
2025-08-03 12:33:45,970 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:33:45,971 [INFO] [cached since 1064s ago] {'rack_id': 'A02'}
2025-08-03 12:33:45,972 [INFO] SELECT COUNT(*) FROM rack_devices WHERE rack_id = %(rack_id)s
2025-08-03 12:33:45,972 [INFO] [cached since 1064s ago] {'rack_id': 'B01'}
2025-08-03 12:33:45,974 [INFO] ROLLBACK
2025-08-03 12:33:45,975 [INFO] [**********.960207] 完成请求: GET http://localhost:5888/api/rack-management/ - 状态码: 200 - 耗时: 0.0157s
2025-08-03 12:33:46,025 [INFO] [1754195626.025672] 开始请求: GET http://localhost:5888/api/rack-management/debug-stats
2025-08-03 12:33:46,029 [INFO] BEGIN (implicit)
2025-08-03 12:33:46,030 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:33:46,031 [INFO] [cached since 1064s ago] {}
2025-08-03 12:33:46,037 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:33:46,039 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,044 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:33:46,045 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,051 [INFO] SELECT id, name, total_u, used_u, temperature FROM racks LIMIT 10
2025-08-03 12:33:46,053 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,056 [INFO] ROLLBACK
2025-08-03 12:33:46,058 [INFO] [1754195626.025672] 完成请求: GET http://localhost:5888/api/rack-management/debug-stats - 状态码: 200 - 耗时: 0.0331s
2025-08-03 12:33:46,068 [INFO] [1754195626.068301] 开始请求: GET http://localhost:5888/api/rack-management/stats
2025-08-03 12:33:46,073 [INFO] BEGIN (implicit)
2025-08-03 12:33:46,075 [INFO] SELECT COUNT(*) FROM racks
2025-08-03 12:33:46,075 [INFO] [cached since 1064s ago] {}
2025-08-03 12:33:46,079 [INFO] SELECT COUNT(*) FROM rack_devices
2025-08-03 12:33:46,081 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,084 [INFO] SELECT COUNT(*) FROM rack_devices WHERE status = 'online'
2025-08-03 12:33:46,085 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,089 [INFO] SELECT SUM(total_u), SUM(used_u) FROM racks
2025-08-03 12:33:46,089 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,091 [INFO] SELECT AVG(temperature) FROM racks
2025-08-03 12:33:46,092 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,095 [INFO] SELECT COUNT(*) FROM racks WHERE status != 'normal'
2025-08-03 12:33:46,095 [INFO] [cached since 1050s ago] {}
2025-08-03 12:33:46,099 [INFO] ROLLBACK
2025-08-03 12:33:46,103 [INFO] [1754195626.068301] 完成请求: GET http://localhost:5888/api/rack-management/stats - 状态码: 200 - 耗时: 0.0351s
2025-08-03 12:33:47,079 [INFO] [**********.079677] 开始请求: GET http://localhost:5888/api/datacenter/
2025-08-03 12:33:47,080 [INFO] [**********.080183] 开始请求: GET http://localhost:5888/api/rack-management/A01
2025-08-03 12:33:47,082 [INFO] BEGIN (implicit)
2025-08-03 12:33:47,082 [INFO] SELECT datacenters.id AS datacenters_id, datacenters.name AS datacenters_name, datacenters.location AS datacenters_location, datacenters.description AS datacenters_description, datacenters.created_at AS datacenters_created_at, datacenters.updated_at AS datacenters_updated_at 
FROM datacenters 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 12:33:47,083 [INFO] BEGIN (implicit)
2025-08-03 12:33:47,083 [INFO] [cached since 1065s ago] {'param_1': 0, 'param_2': 100}
2025-08-03 12:33:47,084 [INFO] SELECT racks.id AS racks_id 
FROM racks 
WHERE racks.id = %(id_1)s 
 LIMIT %(param_1)s
2025-08-03 12:33:47,084 [INFO] [cached since 1044s ago] {'id_1': 'A01', 'param_1': 1}
2025-08-03 12:33:47,085 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:47,085 [INFO] [cached since 1065s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:33:47,086 [INFO] 
            SELECT id, name, location, datacenter_id, total_u, used_u, width, depth, 
                   temperature, humidity, power, max_power, status, description,
                   created_at, updated_at
            FROM racks
            WHERE id = %(rack_id)s
        
2025-08-03 12:33:47,086 [INFO] [cached since 1044s ago] {'rack_id': 'A01'}
2025-08-03 12:33:47,087 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:47,087 [INFO] [cached since 1065s ago] {'datacenter_id_1': '6d55b7d1-609b-4a11-99fd-8fcc10cec08c'}
2025-08-03 12:33:47,087 [INFO] 
            SELECT id, name, type, model, position, u_size, power,
                   weight, status, temperature, description, rack_id,
                   serial_number, ip_address, created_at, updated_at
            FROM rack_devices
            WHERE rack_id = %(rack_id)s
        
2025-08-03 12:33:47,088 [INFO] [cached since 1044s ago] {'rack_id': 'A01'}
2025-08-03 12:33:47,088 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:47,089 [INFO] [cached since 1065s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:33:47,090 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:47,091 [INFO] [cached since 1065s ago] {'datacenter_id_1': '9cc737ad-25a8-4e62-8b74-c6eeab5b19c7'}
2025-08-03 12:33:47,092 [INFO] ROLLBACK
2025-08-03 12:33:47,093 [INFO] SELECT count(racks.id) AS count_1 
FROM racks 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:47,093 [INFO] [**********.080183] 完成请求: GET http://localhost:5888/api/rack-management/A01 - 状态码: 200 - 耗时: 0.0134s
2025-08-03 12:33:47,093 [INFO] [cached since 1065s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:33:47,097 [INFO] SELECT count(rack_devices.id) AS count_1 
FROM rack_devices INNER JOIN racks ON rack_devices.rack_id = racks.id 
WHERE racks.datacenter_id = %(datacenter_id_1)s
2025-08-03 12:33:47,098 [INFO] [cached since 1065s ago] {'datacenter_id_1': 'f62957f0-910c-433f-ac2a-7017f7cc2481'}
2025-08-03 12:33:47,101 [INFO] ROLLBACK
2025-08-03 12:33:47,102 [INFO] [**********.079677] 完成请求: GET http://localhost:5888/api/datacenter/ - 状态码: 200 - 耗时: 0.0224s
2025-08-03 12:45:52,446 [INFO] [**********.446753] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:45:52,504 [INFO] [**********.446753] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.0578s
2025-08-03 12:45:52,506 [INFO] [**********.506678] 开始请求: GET http://localhost:5888/api/license/details
2025-08-03 12:45:52,560 [INFO] [**********.506678] 完成请求: GET http://localhost:5888/api/license/details - 状态码: 200 - 耗时: 0.0541s
2025-08-03 12:45:52,628 [INFO] [**********.628925] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:45:52,686 [INFO] [**********.628925] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.0577s
2025-08-03 12:45:52,688 [INFO] [**********.688685] 开始请求: GET http://localhost:5888/api/license/machine-code
2025-08-03 12:45:52,689 [INFO] [**********.688685] 完成请求: GET http://localhost:5888/api/license/machine-code - 状态码: 200 - 耗时: 0.0010s
2025-08-03 12:45:52,697 [INFO] [**********.697024] 开始请求: GET http://localhost:5888/api/license/status
2025-08-03 12:45:52,752 [INFO] [**********.697024] 完成请求: GET http://localhost:5888/api/license/status - 状态码: 200 - 耗时: 0.0546s
